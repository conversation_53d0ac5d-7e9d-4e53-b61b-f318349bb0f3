# Phase4 AS4 Invoice Sender

A Spring Boot application that demonstrates how to send UBL invoices via AS4/Peppol using the Phase4 library.

## Features

✅ **Lightweight & Embeddable**: Pure Java library that can be integrated into any service  
✅ **Peppol & CEF AS4 Compliant**: Fully compliant with Peppol and CEF AS4 specifications  
✅ **No External Gateway**: No need for heavy external AS4 gateways or UIs  
✅ **Spring Boot Compatible**: Easy integration with Spring Boot applications  
✅ **Custom Logic Control**: Full control over AS4 message creation and sending logic  

## Project Structure

```
src/
├── main/
│   ├── java/org/example/
│   │   ├── Main.java                          # Spring Boot main application
│   │   ├── AS4InvoiceSender.java              # Main AS4 sender service
│   │   ├── config/AS4Configuration.java       # AS4 configuration
│   │   ├── model/AS4MessageInfo.java          # AS4 message model
│   │   └── util/InvoiceProcessor.java         # UBL invoice processor
│   └── resources/
│       ├── application.properties             # Configuration properties
│       ├── invoice.xml                        # Sample UBL invoice
│       └── keystore/                          # Certificate storage
└── test/
    └── java/org/example/
        └── AS4InvoiceSenderTest.java          # Unit tests
```

## Sample UBL Invoice

The project includes a sample UBL 2.3 invoice (`src/main/resources/invoice.xml`) with:
- **Invoice ID**: INV-2024-001
- **Supplier**: Supplier Company Ltd (9908:*********)
- **Customer**: Customer Company Inc (9908:*********)
- **Amount**: $1,080.00 USD (including 8% tax)
- **Peppol Identifiers**: Properly formatted for AS4 transmission

## Quick Start

### 1. Clone and Build

```bash
git clone <repository-url>
cd phase4
mvn clean install
```

### 2. Configure Certificates

For testing, generate self-signed certificates:

```bash
cd src/main/resources/keystore

# Generate keystore
keytool -genkeypair -alias as4 -keyalg RSA -keysize 2048 \
        -validity 365 -keystore as4-keystore.p12 \
        -storetype PKCS12 -storepass changeit -keypass changeit \
        -dname "CN=Test AS4 Certificate, O=Test Organization, C=US"

# Create truststore
keytool -exportcert -alias as4 -keystore as4-keystore.p12 \
        -storetype PKCS12 -storepass changeit -file as4-test-cert.crt
keytool -importcert -alias as4-test -keystore as4-truststore.p12 \
        -storetype PKCS12 -storepass changeit \
        -file as4-test-cert.crt -noprompt
```

### 3. Run the Application

```bash
mvn spring-boot:run
```

The application will:
1. Process the sample UBL invoice
2. Extract AS4 message information
3. Display the conversion results
4. (Optionally) Send the message via AS4

## Configuration

Key configuration properties in `application.properties`:

```properties
# AS4 Endpoint
as4.endpoint.url=https://test-as4.peppol.at/as4

# Participant IDs (must match your UBL invoice)
peppol.sender.participant.id=9908:*********
peppol.receiver.participant.id=9908:*********

# Certificate Configuration
as4.keystore.path=keystore/as4-keystore.p12
as4.keystore.password=changeit
```

## Usage Examples

### Basic Invoice Processing

```java
@Autowired
private AS4InvoiceSender as4InvoiceSender;

// Process and get message info
AS4MessageInfo messageInfo = as4InvoiceSender.getMessageInfo("invoice.xml");
System.out.println("Invoice ID: " + messageInfo.getInvoiceId());
System.out.println("Sender: " + messageInfo.getSenderParticipantId());
```

### Send Invoice via AS4

```java
// Send to default Peppol network
boolean success = as4InvoiceSender.sendInvoice("invoice.xml");

// Send to custom endpoint
boolean success = as4InvoiceSender.sendInvoiceToEndpoint(
    "invoice.xml", 
    "https://custom-as4-endpoint.com/as4"
);
```

### Custom Invoice Processing

```java
@Autowired
private InvoiceProcessor invoiceProcessor;

// Process from file
AS4MessageInfo info = invoiceProcessor.processInvoiceFromFile("/path/to/invoice.xml");

// Process from bytes
byte[] invoiceData = Files.readAllBytes(Path.of("invoice.xml"));
AS4MessageInfo info = invoiceProcessor.processInvoiceFromBytes(invoiceData);
```

## Testing

Run the test suite:

```bash
mvn test
```

The tests cover:
- UBL invoice parsing and validation
- AS4 message info extraction
- Participant ID extraction
- Document type and process identification

## Production Deployment

For production use:

1. **Obtain Peppol Certificates**: Get valid certificates from a Peppol-authorized CA
2. **Update Configuration**: Set production endpoints and participant IDs
3. **Secure Keystores**: Use strong passwords and proper file permissions
4. **Network Configuration**: Ensure firewall rules allow AS4 communication
5. **Monitoring**: Implement logging and monitoring for message delivery

## Dependencies

Key dependencies used:

- **Phase4**: `com.helger.phase4:phase4-lib:2.7.4`
- **Phase4 Peppol**: `com.helger.phase4:phase4-peppol-client:2.7.4`
- **UBL Processing**: `com.helger.ubl:ph-ubl23:8.0.0`
- **Spring Boot**: `3.2.0`

## Troubleshooting

Common issues and solutions:

1. **Certificate Errors**: Check keystore paths and passwords
2. **Network Timeouts**: Verify AS4 endpoint URLs and network connectivity
3. **Validation Failures**: Ensure UBL invoice format compliance
4. **Participant ID Mismatches**: Verify Peppol participant identifiers

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Check the Phase4 documentation: https://github.com/phax/phase4
- Review Peppol AS4 specifications
- Open an issue in this repository
