{"info": {"_postman_id": "as4-complete-workflow-2024", "name": "AS4 Complete Workflow - Real Endpoints", "description": "Complete AS4 workflow testing based on actual controller endpoints: XML generation, validation, AS4 sending, and reverse flow testing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🌍 Configuration & Status", "item": [{"name": "Get Supported Configurations", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reverse-flow/supported-configs", "host": ["{{base_url}}"], "path": ["reverse-flow", "supported-configs"]}}, "response": []}, {"name": "Get Configuration Details - DE/INVOICE", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reverse-flow/config/DE/INVOICE", "host": ["{{base_url}}"], "path": ["reverse-flow", "config", "DE", "INVOICE"]}}, "response": []}, {"name": "Get AS4 System Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/invoice/as4/status", "host": ["{{base_url}}"], "path": ["api", "invoice", "as4", "status"]}}, "response": []}, {"name": "Reverse Flow Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reverse-flow/health", "host": ["{{base_url}}"], "path": ["reverse-flow", "health"]}}, "response": []}]}, {"name": "📄 XML Generation - Peppol SBD Invoice", "item": [{"name": "Generate Invoice XML", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "pm.test(\"Contains UBL Invoice\", function () {", "    pm.expect(pm.response.text()).to.include(\"Invoice\");", "});", "", "// Store the generated XML for next requests", "pm.globals.set(\"generated_invoice_xml\", pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"invoiceNumber\": \"INV-2024-001\",\n  \"issueDate\": \"2024-01-15\",\n  \"supplierName\": \"Test Supplier Ltd\",\n  \"supplierEndpointId\": \"9915:test-sender\",\n  \"supplierAddress\": {\n    \"street\": \"123 Supplier Street\",\n    \"city\": \"Berlin\",\n    \"postalCode\": \"10115\",\n    \"country\": \"DE\"\n  },\n  \"customerName\": \"Test Customer AG\",\n  \"customerEndpointId\": \"9922:test-receiver\",\n  \"customerAddress\": {\n    \"street\": \"456 Customer Avenue\",\n    \"city\": \"Munich\",\n    \"postalCode\": \"80331\",\n    \"country\": \"DE\"\n  },\n  \"lineItems\": [\n    {\n      \"description\": \"Test Product\",\n      \"quantity\": 10,\n      \"unitPrice\": 100.00,\n      \"taxRate\": 19.0\n    }\n  ],\n  \"currency\": \"EUR\"\n}"}, "url": {"raw": "{{base_url}}/api/peppol-sbd-invoice/generate", "host": ["{{base_url}}"], "path": ["api", "peppol-sbd-invoice", "generate"]}}, "response": []}, {"name": "Generate Invoice XML with Country Config", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "// Store the generated XML for next requests", "pm.globals.set(\"generated_country_invoice_xml\", pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"countryCode\": \"DE\",\n  \"documentType\": \"INVOICE\",\n  \"invoiceNumber\": \"DE-INV-2024-001\",\n  \"issueDate\": \"2024-01-15\",\n  \"supplierName\": \"German Supplier GmbH\",\n  \"supplierEndpointId\": \"9915:test-sender-de\",\n  \"supplierAddress\": {\n    \"street\": \"Musterstraße 123\",\n    \"city\": \"Berlin\",\n    \"postalCode\": \"10115\",\n    \"country\": \"DE\"\n  },\n  \"customerName\": \"German Customer AG\",\n  \"customerEndpointId\": \"9922:test-receiver-de\",\n  \"customerAddress\": {\n    \"street\": \"Kundenstraße 456\",\n    \"city\": \"Munich\",\n    \"postalCode\": \"80331\",\n    \"country\": \"DE\"\n  },\n  \"lineItems\": [\n    {\n      \"description\": \"German Product\",\n      \"quantity\": 10,\n      \"unitPrice\": 100.00,\n      \"taxRate\": 19.0\n    }\n  ],\n  \"currency\": \"EUR\"\n}"}, "url": {"raw": "{{base_url}}/api/peppol-sbd-invoice/generate-with-config", "host": ["{{base_url}}"], "path": ["api", "peppol-sbd-invoice", "generate-with-config"]}}, "response": []}, {"name": "Generate Italian CreditNote XML", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "// Store the generated XML for next requests", "pm.globals.set(\"generated_it_creditnote_xml\", pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"countryCode\": \"IT\",\n  \"documentType\": \"CREDITNOTE\",\n  \"invoiceNumber\": \"IT-CN-2024-001\",\n  \"issueDate\": \"2024-01-15\",\n  \"supplierName\": \"Fornitore Italiano SRL\",\n  \"supplierEndpointId\": \"9915:test-sender-it\",\n  \"supplierAddress\": {\n    \"street\": \"Via Roma 123\",\n    \"city\": \"Rome\",\n    \"postalCode\": \"00100\",\n    \"country\": \"IT\"\n  },\n  \"customerName\": \"Cliente Italiano SpA\",\n  \"customerEndpointId\": \"9922:test-receiver-it\",\n  \"customerAddress\": {\n    \"street\": \"Via Milano 456\",\n    \"city\": \"Milan\",\n    \"postalCode\": \"20100\",\n    \"country\": \"IT\"\n  },\n  \"lineItems\": [\n    {\n      \"description\": \"Prodotto Italiano\",\n      \"quantity\": 3,\n      \"unitPrice\": 150.00,\n      \"taxRate\": 22.0\n    }\n  ],\n  \"currency\": \"EUR\"\n}"}, "url": {"raw": "{{base_url}}/api/peppol-sbd-invoice/generate-with-config", "host": ["{{base_url}}"], "path": ["api", "peppol-sbd-invoice", "generate-with-config"]}}, "response": []}]}, {"name": "✅ XML Validation - Country Specific", "item": [{"name": "Validate German Invoice XML", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Validation passed\", function () {", "    const response = pm.response.json();", "    pm.expect(response.valid).to.be.true;", "});", "", "pm.test(\"No validation errors\", function () {", "    const response = pm.response.json();", "    pm.expect(response.errors).to.be.empty;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_de_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/validation/validate-with-config/DE/INVOICE", "host": ["{{base_url}}"], "path": ["api", "validation", "validate-with-config", "DE", "INVOICE"]}}, "response": []}, {"name": "Validate French Invoice XML", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Validation passed\", function () {", "    const response = pm.response.json();", "    pm.expect(response.valid).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_fr_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/validation/validate-with-config/FR/INVOICE", "host": ["{{base_url}}"], "path": ["api", "validation", "validate-with-config", "FR", "INVOICE"]}}, "response": []}, {"name": "Validate Italian CreditNote XML", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Validation passed\", function () {", "    const response = pm.response.json();", "    pm.expect(response.valid).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_it_creditnote_xml}}"}, "url": {"raw": "{{base_url}}/api/validation/validate-with-config/IT/CREDITNOTE", "host": ["{{base_url}}"], "path": ["api", "validation", "validate-with-config", "IT", "CREDITNOTE"]}}, "response": []}]}, {"name": "🚀 AS4 Sending - Country Specific", "item": [{"name": "Send German Invoice via AS4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"AS4 message sent successfully\", function () {", "    pm.expect(pm.response.text()).to.include(\"successfully\");", "});", "", "// Store message ID for tracking", "const response = pm.response.text();", "const messageIdMatch = response.match(/Message ID: ([\\w-]+)/);", "if (messageIdMatch) {", "    pm.globals.set(\"de_message_id\", messageIdMatch[1]);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}, {"key": "X-Country-Code", "value": "DE"}, {"key": "X-Document-Type", "value": "INVOICE"}], "body": {"mode": "raw", "raw": "{{generated_de_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/as4/send-with-config/DE/INVOICE", "host": ["{{base_url}}"], "path": ["api", "as4", "send-with-config", "DE", "INVOICE"]}}, "response": []}, {"name": "Send French Invoice via AS4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"AS4 message sent successfully\", function () {", "    pm.expect(pm.response.text()).to.include(\"successfully\");", "});", "", "// Store message ID for tracking", "const response = pm.response.text();", "const messageIdMatch = response.match(/Message ID: ([\\w-]+)/);", "if (messageIdMatch) {", "    pm.globals.set(\"fr_message_id\", messageIdMatch[1]);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}, {"key": "X-Country-Code", "value": "FR"}, {"key": "X-Document-Type", "value": "INVOICE"}], "body": {"mode": "raw", "raw": "{{generated_fr_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/as4/send-with-config/FR/INVOICE", "host": ["{{base_url}}"], "path": ["api", "as4", "send-with-config", "FR", "INVOICE"]}}, "response": []}, {"name": "Send Italian CreditNote via AS4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"AS4 message sent successfully\", function () {", "    pm.expect(pm.response.text()).to.include(\"successfully\");", "});", "", "// Store message ID for tracking", "const response = pm.response.text();", "const messageIdMatch = response.match(/Message ID: ([\\w-]+)/);", "if (messageIdMatch) {", "    pm.globals.set(\"it_message_id\", messageIdMatch[1]);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}, {"key": "X-Country-Code", "value": "IT"}, {"key": "X-Document-Type", "value": "CREDITNOTE"}], "body": {"mode": "raw", "raw": "{{generated_it_creditnote_xml}}"}, "url": {"raw": "{{base_url}}/api/as4/send-with-config/IT/CREDITNOTE", "host": ["{{base_url}}"], "path": ["api", "as4", "send-with-config", "IT", "CREDITNOTE"]}}, "response": []}, {"name": "Check AS4 Message Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Message status retrieved\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('messageId');", "    pm.expect(response).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/as4/status/{{de_message_id}}", "host": ["{{base_url}}"], "path": ["api", "as4", "status", "{{de_message_id}}"]}}, "response": []}]}, {"name": "🔄 Reverse Flow Testing", "item": [{"name": "Receive Message - Auto Detection (NEW)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "pm.test(\"Auto-detection successful\", function () {", "    pm.expect(pm.response.text()).to.include(\"ApplicationResponse\");", "});", "", "// Store the response for further testing", "pm.globals.set(\"reverse_flow_response\", pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_de_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/reverse-flow/receive-message-auto-detect", "host": ["{{base_url}}"], "path": ["api", "reverse-flow", "receive-message-auto-detect"]}}, "response": []}, {"name": "Receive German Message - Legacy Endpoint", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "pm.test(\"German-specific processing\", function () {", "    pm.expect(pm.response.text()).to.include(\"ApplicationResponse\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_de_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/reverse-flow/receive-message/DE/INVOICE", "host": ["{{base_url}}"], "path": ["api", "reverse-flow", "receive-message", "DE", "INVOICE"]}}, "response": []}, {"name": "Receive French Message - Auto Detection", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"French-specific processing\", function () {", "    pm.expect(pm.response.text()).to.include(\"ApplicationResponse\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_fr_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/reverse-flow/receive-message-auto-detect", "host": ["{{base_url}}"], "path": ["api", "reverse-flow", "receive-message-auto-detect"]}}, "response": []}, {"name": "Receive Italian CreditNote - Auto Detection", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Italian CreditNote processing\", function () {", "    pm.expect(pm.response.text()).to.include(\"ApplicationResponse\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_it_creditnote_xml}}"}, "url": {"raw": "{{base_url}}/api/reverse-flow/receive-message-auto-detect", "host": ["{{base_url}}"], "path": ["api", "reverse-flow", "receive-message-auto-detect"]}}, "response": []}]}, {"name": "🧪 Testing & Validation", "item": [{"name": "Test SBDH with Country Scope", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"SBDH contains country scope\", function () {", "    pm.expect(pm.response.text()).to.include(\"COUNTRY_C1\");", "    pm.expect(pm.response.text()).to.include(\"<InstanceIdentifier>DE</InstanceIdentifier>\");", "});", "", "pm.test(\"SBDH contains document type\", function () {", "    pm.expect(pm.response.text()).to.include(\"DOCUMENTID\");", "    pm.expect(pm.response.text()).to.include(\"Invoice\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<StandardBusinessDocument xmlns=\"http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader\">\n    <StandardBusinessDocumentHeader>\n        <HeaderVersion>1.0</HeaderVersion>\n        <Sender>\n            <Identifier Authority=\"iso6523-actorid-upis\">9915:test-sender-de</Identifier>\n        </Sender>\n        <Receiver>\n            <Identifier Authority=\"iso6523-actorid-upis\">9922:test-receiver-de</Identifier>\n        </Receiver>\n        <DocumentIdentification>\n            <Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</Standard>\n            <TypeVersion>2.1</TypeVersion>\n            <InstanceIdentifier>DE-INV-2024-001</InstanceIdentifier>\n            <Type>Invoice</Type>\n            <CreationDateAndTime>2024-01-15T10:00:00Z</CreationDateAndTime>\n        </DocumentIdentification>\n        <BusinessScope>\n            <Scope>\n                <Type>DOCUMENTID</Type>\n                <InstanceIdentifier>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</InstanceIdentifier>\n                <Identifier>busdox-docid-qns</Identifier>\n            </Scope>\n            <Scope>\n                <Type>PROCESSID</Type>\n                <InstanceIdentifier>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</InstanceIdentifier>\n                <Identifier>cenbii-procid-ubl</Identifier>\n            </Scope>\n            <Scope>\n                <Type>COUNTRY_C1</Type>\n                <InstanceIdentifier>DE</InstanceIdentifier>\n            </Scope>\n        </BusinessScope>\n    </StandardBusinessDocumentHeader>\n    <Invoice xmlns=\"urn:oasis:names:specification:ubl:schema:xsd:Invoice-2\"\n             xmlns:cbc=\"urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2\"\n             xmlns:cac=\"urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2\">\n        <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0</cbc:CustomizationID>\n        <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>\n        <cbc:ID>DE-INV-2024-001</cbc:ID>\n        <cbc:IssueDate>2024-01-15</cbc:IssueDate>\n    </Invoice>\n</StandardBusinessDocument>"}, "url": {"raw": "{{base_url}}/api/reverse-flow/receive-message-auto-detect", "host": ["{{base_url}}"], "path": ["api", "reverse-flow", "receive-message-auto-detect"]}}, "response": []}, {"name": "Test Error <PERSON> - Invalid Country", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400 or 500\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 500]);", "});", "", "pm.test(\"Error message contains country info\", function () {", "    pm.expect(pm.response.text()).to.include(\"country\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"countryCode\": \"XX\",\n  \"documentType\": \"INVOICE\",\n  \"invoiceNumber\": \"TEST-001\",\n  \"issueDate\": \"2024-01-15\"\n}"}, "url": {"raw": "{{base_url}}/api/peppol-sbd-invoice/generate-with-config", "host": ["{{base_url}}"], "path": ["api", "peppol-sbd-invoice", "generate-with-config"]}}, "response": []}, {"name": "Test Error Handling - Invalid Document Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400 or 500\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 500]);", "});", "", "pm.test(\"Error message contains document type info\", function () {", "    pm.expect(pm.response.text()).to.include(\"document\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"countryCode\": \"DE\",\n  \"documentType\": \"INVALID_TYPE\",\n  \"invoiceNumber\": \"TEST-001\",\n  \"issueDate\": \"2024-01-15\"\n}"}, "url": {"raw": "{{base_url}}/api/peppol-sbd-invoice/generate-with-config", "host": ["{{base_url}}"], "path": ["api", "peppol-sbd-invoice", "generate-with-config"]}}, "response": []}]}, {"name": "📊 Monitoring & Status", "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Application is healthy\", function () {", "    const response = pm.response.json();", "    pm.expect(response.status).to.eql(\"UP\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/actuator/health", "host": ["{{base_url}}"], "path": ["actuator", "health"]}}, "response": []}, {"name": "Get AS4 Configuration Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"AS4 configuration loaded\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('mode');", "    pm.expect(response).to.have.property('security');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/as4/config/status", "host": ["{{base_url}}"], "path": ["api", "as4", "config", "status"]}}, "response": []}, {"name": "Get Country Configuration Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Country configurations loaded\", function () {", "    const response = pm.response.json();", "    pm.expect(response.countries).to.be.an('array');", "    pm.expect(response.countries.length).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/config/status", "host": ["{{base_url}}"], "path": ["api", "config", "status"]}}, "response": []}, {"name": "Get Recent AS4 Messages", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Messages list retrieved\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/as4/messages/recent?limit=10", "host": ["{{base_url}}"], "path": ["api", "as4", "messages", "recent"], "query": [{"key": "limit", "value": "10"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default base URL if not already set", "if (!pm.globals.get(\"base_url\")) {", "    pm.globals.set(\"base_url\", \"http://localhost:8080\");", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}]}