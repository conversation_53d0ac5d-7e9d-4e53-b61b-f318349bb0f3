#!/usr/bin/env node

/**
 * AS4 Complete Workflow Test Runner
 * 
 * This script runs the AS4 Postman collection programmatically using Newman
 * 
 * Prerequisites:
 * - npm install -g newman
 * - AS4 application running on localhost:8080
 * 
 * Usage:
 * - node run-tests.js
 * - node run-tests.js --environment=production
 * - node run-tests.js --country=FR --document-type=INVOICE
 */

const newman = require('newman');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
    collection: path.join(__dirname, 'AS4_Complete_Workflow.postman_collection.json'),
    environment: path.join(__dirname, 'AS4_Environment.postman_environment.json'),
    baseUrl: process.env.AS4_BASE_URL || 'http://localhost:8080',
    country: process.env.AS4_TEST_COUNTRY || 'DE',
    documentType: process.env.AS4_TEST_DOCUMENT_TYPE || 'INVOICE',
    timeout: parseInt(process.env.AS4_TEST_TIMEOUT) || 30000,
    verbose: process.env.AS4_TEST_VERBOSE === 'true'
};

// Parse command line arguments
const args = process.argv.slice(2);
args.forEach(arg => {
    if (arg.startsWith('--environment=')) {
        const env = arg.split('=')[1];
        if (env === 'production') {
            config.baseUrl = 'https://your-production-url.com';
        } else if (env === 'staging') {
            config.baseUrl = 'https://your-staging-url.com';
        }
    } else if (arg.startsWith('--country=')) {
        config.country = arg.split('=')[1];
    } else if (arg.startsWith('--document-type=')) {
        config.documentType = arg.split('=')[1];
    } else if (arg === '--verbose') {
        config.verbose = true;
    }
});

console.log('🚀 AS4 Complete Workflow Test Runner');
console.log('=====================================');
console.log(`📍 Base URL: ${config.baseUrl}`);
console.log(`🌍 Country: ${config.country}`);
console.log(`📄 Document Type: ${config.documentType}`);
console.log(`⏱️  Timeout: ${config.timeout}ms`);
console.log('');

// Check if files exist
if (!fs.existsSync(config.collection)) {
    console.error('❌ Collection file not found:', config.collection);
    process.exit(1);
}

if (!fs.existsSync(config.environment)) {
    console.error('❌ Environment file not found:', config.environment);
    process.exit(1);
}

// Create custom environment with runtime values
const environmentData = JSON.parse(fs.readFileSync(config.environment, 'utf8'));
environmentData.values.forEach(variable => {
    if (variable.key === 'base_url') {
        variable.value = config.baseUrl;
    } else if (variable.key === 'default_country') {
        variable.value = config.country;
    } else if (variable.key === 'default_document_type') {
        variable.value = config.documentType;
    }
});

// Test execution options
const runOptions = {
    collection: config.collection,
    environment: environmentData,
    timeout: config.timeout,
    timeoutRequest: config.timeout,
    timeoutScript: 10000,
    delayRequest: 1000, // 1 second delay between requests
    reporters: ['cli', 'json'],
    reporter: {
        json: {
            export: path.join(__dirname, 'test-results.json')
        }
    }
};

if (config.verbose) {
    runOptions.reporters.push('html');
    runOptions.reporter.html = {
        export: path.join(__dirname, 'test-results.html')
    };
}

// Run the collection
console.log('🏃 Running AS4 Complete Workflow Tests...');
console.log('');

newman.run(runOptions, function (err, summary) {
    if (err) {
        console.error('❌ Test execution failed:', err);
        process.exit(1);
    }

    console.log('');
    console.log('📊 Test Execution Summary');
    console.log('=========================');
    
    const stats = summary.run.stats;
    const failures = summary.run.failures;
    
    console.log(`✅ Total Requests: ${stats.requests.total}`);
    console.log(`✅ Successful Requests: ${stats.requests.total - stats.requests.failed}`);
    console.log(`❌ Failed Requests: ${stats.requests.failed}`);
    console.log(`🧪 Total Tests: ${stats.tests.total}`);
    console.log(`✅ Passed Tests: ${stats.tests.total - stats.tests.failed}`);
    console.log(`❌ Failed Tests: ${stats.tests.failed}`);
    console.log(`📊 Total Assertions: ${stats.assertions.total}`);
    console.log(`✅ Passed Assertions: ${stats.assertions.total - stats.assertions.failed}`);
    console.log(`❌ Failed Assertions: ${stats.assertions.failed}`);
    
    if (failures && failures.length > 0) {
        console.log('');
        console.log('❌ Test Failures:');
        console.log('==================');
        failures.forEach((failure, index) => {
            console.log(`${index + 1}. ${failure.source.name || 'Unknown'}`);
            console.log(`   Error: ${failure.error.message}`);
            if (failure.error.test) {
                console.log(`   Test: ${failure.error.test}`);
            }
            console.log('');
        });
    }
    
    // Generate summary report
    const report = {
        timestamp: new Date().toISOString(),
        config: config,
        summary: {
            total: stats.requests.total,
            successful: stats.requests.total - stats.requests.failed,
            failed: stats.requests.failed,
            tests: {
                total: stats.tests.total,
                passed: stats.tests.total - stats.tests.failed,
                failed: stats.tests.failed
            },
            assertions: {
                total: stats.assertions.total,
                passed: stats.assertions.total - stats.assertions.failed,
                failed: stats.assertions.failed
            }
        },
        failures: failures || []
    };
    
    fs.writeFileSync(
        path.join(__dirname, 'test-summary.json'), 
        JSON.stringify(report, null, 2)
    );
    
    console.log('');
    console.log('📁 Reports Generated:');
    console.log('=====================');
    console.log(`📄 JSON Results: ${path.join(__dirname, 'test-results.json')}`);
    console.log(`📄 Summary: ${path.join(__dirname, 'test-summary.json')}`);
    
    if (config.verbose) {
        console.log(`📄 HTML Report: ${path.join(__dirname, 'test-results.html')}`);
    }
    
    console.log('');
    
    if (stats.requests.failed > 0 || stats.tests.failed > 0) {
        console.log('❌ Some tests failed. Check the reports for details.');
        process.exit(1);
    } else {
        console.log('✅ All tests passed successfully!');
        process.exit(0);
    }
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Test execution interrupted by user');
    process.exit(1);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Test execution terminated');
    process.exit(1);
});
