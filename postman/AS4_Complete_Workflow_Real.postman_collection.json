{"info": {"_postman_id": "as4-complete-workflow-real-2024", "name": "AS4 Complete Workflow - Real Endpoints", "description": "Complete AS4 workflow testing based on actual controller endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "📄 Peppol SBD Invoice Controller", "item": [{"name": "Generate Invoice XML", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "// Store the generated XML for next requests", "pm.globals.set(\"generated_invoice_xml\", pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"invoiceNumber\": \"INV-2024-001\",\n  \"issueDate\": \"2024-01-15\",\n  \"supplierName\": \"Test Supplier Ltd\",\n  \"supplierEndpointId\": \"9915:test-sender\",\n  \"supplierAddress\": {\n    \"street\": \"123 Supplier Street\",\n    \"city\": \"Berlin\",\n    \"postalCode\": \"10115\",\n    \"country\": \"DE\"\n  },\n  \"customerName\": \"Test Customer AG\",\n  \"customerEndpointId\": \"9922:test-receiver\",\n  \"customerAddress\": {\n    \"street\": \"456 Customer Avenue\",\n    \"city\": \"Munich\",\n    \"postalCode\": \"80331\",\n    \"country\": \"DE\"\n  },\n  \"lineItems\": [\n    {\n      \"description\": \"Test Product\",\n      \"quantity\": 10,\n      \"unitPrice\": 100.00,\n      \"taxRate\": 19.0\n    }\n  ],\n  \"currency\": \"EUR\"\n}"}, "url": {"raw": "{{base_url}}/api/peppol-sbd-invoice/generate", "host": ["{{base_url}}"], "path": ["api", "peppol-sbd-invoice", "generate"]}}, "response": []}, {"name": "Generate Invoice XML with Country Config", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "// Store the generated XML for next requests", "pm.globals.set(\"generated_country_invoice_xml\", pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"countryCode\": \"DE\",\n  \"documentType\": \"INVOICE\",\n  \"invoiceNumber\": \"DE-INV-2024-001\",\n  \"issueDate\": \"2024-01-15\",\n  \"supplierName\": \"German Supplier GmbH\",\n  \"supplierEndpointId\": \"9915:test-sender-de\",\n  \"supplierAddress\": {\n    \"street\": \"Musterstraße 123\",\n    \"city\": \"Berlin\",\n    \"postalCode\": \"10115\",\n    \"country\": \"DE\"\n  },\n  \"customerName\": \"German Customer AG\",\n  \"customerEndpointId\": \"9922:test-receiver-de\",\n  \"customerAddress\": {\n    \"street\": \"Kundenstraße 456\",\n    \"city\": \"Munich\",\n    \"postalCode\": \"80331\",\n    \"country\": \"DE\"\n  },\n  \"lineItems\": [\n    {\n      \"description\": \"German Product\",\n      \"quantity\": 10,\n      \"unitPrice\": 100.00,\n      \"taxRate\": 19.0\n    }\n  ],\n  \"currency\": \"EUR\"\n}"}, "url": {"raw": "{{base_url}}/api/peppol-sbd-invoice/generate-with-config", "host": ["{{base_url}}"], "path": ["api", "peppol-sbd-invoice", "generate-with-config"]}}, "response": []}, {"name": "Send Invoice via AS4", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"AS4 message sent successfully\", function () {", "    pm.expect(pm.response.text()).to.include(\"successfully\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/peppol-sbd-invoice/send-as4", "host": ["{{base_url}}"], "path": ["api", "peppol-sbd-invoice", "send-as4"]}}, "response": []}, {"name": "Receive AS4 Message (Reverse Flow)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "// Store the response for further testing", "pm.globals.set(\"reverse_flow_response\", pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/peppol-sbd-invoice/receive-as4-message", "host": ["{{base_url}}"], "path": ["api", "peppol-sbd-invoice", "receive-as4-message"]}}, "response": []}]}, {"name": "✅ XML Validation Controller", "item": [{"name": "Validate XML with Schematron", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Validation response received\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('valid');", "    pm.expect(response).to.have.property('errors');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/validation/validate", "host": ["{{base_url}}"], "path": ["api", "validation", "validate"]}}, "response": []}, {"name": "Validate XML with Country Config", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Country-specific validation passed\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('valid');", "    pm.expect(response).to.have.property('errors');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_country_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/validation/validate-with-config/DE/INVOICE", "host": ["{{base_url}}"], "path": ["api", "validation", "validate-with-config", "DE", "INVOICE"]}}, "response": []}, {"name": "Validate XML with Custom Schematron", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Custom schematron validation completed\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('valid');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_invoice_xml}}"}, "url": {"raw": "{{base_url}}/api/validation/validate-with-schematron/PEPPOL-EN16931-UBL.sch", "host": ["{{base_url}}"], "path": ["api", "validation", "validate-with-schematron", "PEPPOL-EN16931-UBL.sch"]}}, "response": []}]}, {"name": "🔄 Reverse Flow Controller", "item": [{"name": "Receive Message - Auto Detection (NEW)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "pm.test(\"Auto-detection successful\", function () {", "    pm.expect(pm.response.text()).to.include(\"ApplicationResponse\");", "});", "", "// Store the response for further testing", "pm.globals.set(\"reverse_flow_response\", pm.response.text());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_country_invoice_xml}}"}, "url": {"raw": "{{base_url}}/reverse-flow/receive-message-auto-detect", "host": ["{{base_url}}"], "path": ["reverse-flow", "receive-message-auto-detect"]}}, "response": []}, {"name": "Receive Message with Country Config (Legacy)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is XML\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/xml\");", "});", "", "pm.test(\"Country-specific processing\", function () {", "    pm.expect(pm.response.text()).to.include(\"ApplicationResponse\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "{{generated_country_invoice_xml}}"}, "url": {"raw": "{{base_url}}/reverse-flow/receive-message/DE/INVOICE", "host": ["{{base_url}}"], "path": ["reverse-flow", "receive-message", "DE", "INVOICE"]}}, "response": []}, {"name": "Get Supported Configurations", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Supported configurations retrieved\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reverse-flow/supported-configs", "host": ["{{base_url}}"], "path": ["reverse-flow", "supported-configs"]}}, "response": []}, {"name": "Get Configuration Details", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Configuration details retrieved\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('countryCode');", "    pm.expect(response).to.have.property('documentType');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reverse-flow/config/DE/INVOICE", "host": ["{{base_url}}"], "path": ["reverse-flow", "config", "DE", "INVOICE"]}}, "response": []}, {"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Health check passed\", function () {", "    pm.expect(pm.response.text()).to.include(\"OK\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reverse-flow/health", "host": ["{{base_url}}"], "path": ["reverse-flow", "health"]}}, "response": []}]}, {"name": "📊 Invoice Service & Recipients", "item": [{"name": "Get AS4 System Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"AS4 status retrieved\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/invoice/as4/status", "host": ["{{base_url}}"], "path": ["api", "invoice", "as4", "status"]}}, "response": []}, {"name": "Get Recipient Endpoint", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Recipient endpoint retrieved\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('participantId');", "    pm.expect(response).to.have.property('endpointUrl');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/recipients/9922:test-receiver", "host": ["{{base_url}}"], "path": ["api", "recipients", "9922:test-receiver"]}}, "response": []}, {"name": "Get All Recipients", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Recipients list retrieved\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/recipients", "host": ["{{base_url}}"], "path": ["api", "recipients"]}}, "response": []}]}, {"name": "🧪 Complete Workflow Tests", "item": [{"name": "Test 1: Generate → Validate → Send (Standard Flow)", "event": [{"listen": "prerequest", "script": {"exec": ["// This test combines multiple steps in sequence", "console.log('Starting complete workflow test...');"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Complete workflow test setup\", function () {", "    pm.expect(true).to.be.true;", "});", "", "// This is a placeholder - in practice, you would run the individual requests in sequence"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reverse-flow/health", "host": ["{{base_url}}"], "path": ["reverse-flow", "health"]}}, "response": []}, {"name": "Test 2: Country-Specific Flow (DE)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"German workflow test setup\", function () {", "    pm.expect(true).to.be.true;", "});", "", "// Test German-specific configuration", "pm.globals.set('test_country', 'DE');", "pm.globals.set('test_document_type', 'INVOICE');"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reverse-flow/config/DE/INVOICE", "host": ["{{base_url}}"], "path": ["reverse-flow", "config", "DE", "INVOICE"]}}, "response": []}, {"name": "Test 3: Auto-Detection Flow", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Auto-detection workflow test setup\", function () {", "    pm.expect(true).to.be.true;", "});", "", "// Test auto-detection capabilities"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reverse-flow/supported-configs", "host": ["{{base_url}}"], "path": ["reverse-flow", "supported-configs"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default base URL if not already set", "if (!pm.globals.get(\"base_url\")) {", "    pm.globals.set(\"base_url\", \"http://localhost:8080\");", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}]}