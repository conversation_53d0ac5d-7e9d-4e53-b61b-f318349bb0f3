# AS4 Complete Workflow - Postman Collection

This Postman collection provides comprehensive testing for the AS4 system with country-specific XML generation, validation, AS4 sending, and reverse flow testing.

## 📋 Collection Overview

The collection is organized into the following sections:

### 🌍 Country Configuration
- **Get Available Countries**: Retrieve list of supported countries
- **Get Country Configuration**: Get specific country configuration
- **Get Document Types**: Get supported document types for a country

### 📄 XML Generation - Country Specific
- **Generate German Invoice XML**: Create German-specific invoice XML
- **Generate French Invoice XML**: Create French-specific invoice XML  
- **Generate Italian CreditNote XML**: Create Italian-specific credit note XML

### ✅ XML Validation - Country Specific
- **Validate German Invoice XML**: Validate using German schematron rules
- **Validate French Invoice XML**: Validate using French schematron rules
- **Validate Italian CreditNote XML**: Validate using Italian schematron rules

### 🚀 AS4 Sending - Country Specific
- **Send German Invoice via AS4**: Send German invoice through AS4
- **Send French Invoice via AS4**: Send French invoice through AS4
- **Send Italian CreditNote via AS4**: Send Italian credit note through AS4
- **Check AS4 Message Status**: Monitor message delivery status

### 🔄 Reverse Flow Testing
- **Receive Message - Auto Detection (NEW)**: Automatically detect country/document type from XML
- **Receive German Message - Legacy**: Process German message (legacy endpoint)
- **Receive French Message - Auto Detection**: Process French message with auto-detection
- **Receive Italian CreditNote - Auto Detection**: Process Italian credit note with auto-detection

### 🧪 Testing & Validation
- **Test SBDH with Country Scope**: Test SBDH parsing with country information
- **Test Error Handling**: Test invalid country and document type scenarios

### 📊 Monitoring & Status
- **Health Check**: Application health status
- **Get AS4 Configuration Status**: AS4 system configuration
- **Get Country Configuration Status**: Country configuration status
- **Get Recent AS4 Messages**: Recent message history

## 🚀 Getting Started

### Prerequisites
1. **AS4 Application Running**: Ensure your AS4 application is running on `http://localhost:8080`
2. **Postman Installed**: Download and install Postman
3. **Environment Setup**: Import the provided environment file

### Import Instructions

1. **Import Collection**:
   ```
   File → Import → Upload Files → Select "AS4_Complete_Workflow.postman_collection.json"
   ```

2. **Import Environment**:
   ```
   File → Import → Upload Files → Select "AS4_Environment.postman_environment.json"
   ```

3. **Select Environment**:
   - Click the environment dropdown (top right)
   - Select "AS4 Complete Workflow Environment"

### Configuration

Update the environment variables as needed:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `base_url` | `http://localhost:8080` | AS4 application base URL |
| `as4_mode` | `dummy` | AS4 mode (dummy/production/test) |
| `security_enabled` | `false` | Whether AS4 security is enabled |
| `default_country` | `DE` | Default country for testing |

## 🔄 Workflow Execution

### Complete Workflow Test
Run the requests in this order for a complete test:

1. **🌍 Country Configuration**
   - Start with "Get Available Countries" to see supported countries
   - Check specific country configurations

2. **📄 XML Generation**
   - Generate country-specific XML documents
   - XML is automatically stored in environment variables

3. **✅ XML Validation**
   - Validate generated XML using country-specific rules
   - Ensure compliance with local regulations

4. **🚀 AS4 Sending**
   - Send validated XML through AS4
   - Monitor message status

5. **🔄 Reverse Flow Testing**
   - Test incoming message processing
   - Use auto-detection for country/document type

### Key Features

#### 🤖 Auto-Detection (NEW)
The new auto-detection endpoint automatically determines:
- **Country Code**: From XML content (SBDH BusinessScope or address elements)
- **Document Type**: From XML root element (Invoice, CreditNote, ApplicationResponse)

```http
POST /api/reverse-flow/receive-message-auto-detect
Content-Type: application/xml

<Invoice xmlns="...">
  <!-- System automatically detects this is a German Invoice -->
  <cac:AccountingSupplierParty>
    <cac:Party>
      <cac:PostalAddress>
        <cac:Country>
          <cbc:IdentificationCode>DE</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
    </cac:Party>
  </cac:AccountingSupplierParty>
</Invoice>
```

#### 🌍 Country-Specific Processing
Each country has its own:
- **Participant IDs**: Different sender/receiver IDs per country
- **Validation Rules**: Country-specific schematron files
- **Document Formats**: Localized XML structures
- **AS4 Configuration**: Country-specific AS4 settings

#### 📊 Automatic Testing
The collection includes:
- **Response Validation**: Automatic checks for status codes and content
- **Data Extraction**: Automatic extraction of message IDs and XML content
- **Error Handling**: Tests for invalid scenarios
- **Chain Execution**: Variables passed between requests

## 🧪 Test Scenarios

### Supported Countries
- **🇩🇪 Germany (DE)**: German-specific invoice processing
- **🇫🇷 France (FR)**: French-specific invoice processing  
- **🇮🇹 Italy (IT)**: Italian-specific credit note processing
- **🌐 DEFAULT**: Fallback configuration

### Supported Document Types
- **INVOICE**: Standard invoices
- **CREDITNOTE**: Credit notes
- **APPLICATIONRESPONSE**: Application responses

### Test Data Examples

#### German Invoice
```json
{
  "countryCode": "DE",
  "documentType": "INVOICE",
  "invoiceNumber": "DE-INV-2024-001",
  "supplierEndpointId": "9915:test-sender-de",
  "customerEndpointId": "9922:test-receiver-de",
  "currency": "EUR"
}
```

#### French Invoice
```json
{
  "countryCode": "FR", 
  "documentType": "INVOICE",
  "invoiceNumber": "FR-INV-2024-001",
  "supplierEndpointId": "9915:test-sender-fr",
  "customerEndpointId": "9922:test-receiver-fr",
  "currency": "EUR"
}
```

## 🔧 Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure AS4 application is running
   - Check `base_url` in environment

2. **Validation Failures**
   - Check country/document type combination is supported
   - Verify XML structure matches expected format

3. **AS4 Sending Errors**
   - Ensure AS4 mode is set correctly
   - Check security settings match application configuration

4. **Auto-Detection Issues**
   - Ensure XML contains country information in address or SBDH
   - Check document root element is recognized

### Debug Tips

1. **Enable Postman Console**: View → Show Postman Console
2. **Check Environment Variables**: Ensure variables are populated correctly
3. **Review Response Bodies**: Check error messages for specific issues
4. **Test Individual Requests**: Run requests separately to isolate issues

## 📈 Monitoring

Use the monitoring endpoints to track:
- **Application Health**: System status and availability
- **Message Status**: AS4 message delivery tracking
- **Configuration Status**: Country and AS4 configuration health
- **Recent Messages**: Message history and patterns

## 🎯 Best Practices

1. **Run in Sequence**: Execute requests in the recommended order
2. **Check Variables**: Verify environment variables are set correctly
3. **Monitor Responses**: Review test results and response content
4. **Use Auto-Detection**: Prefer the new auto-detection endpoint for reverse flow
5. **Test Error Scenarios**: Include invalid data tests in your workflow

This collection provides a comprehensive testing framework for the AS4 system with full country-specific support and automatic configuration detection! 🚀
