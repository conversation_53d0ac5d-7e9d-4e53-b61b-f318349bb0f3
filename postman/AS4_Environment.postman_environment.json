{"id": "as4-environment-2024", "name": "AS4 Complete Workflow Environment", "values": [{"key": "base_url", "value": "http://localhost:8080", "description": "Base URL for the AS4 application", "type": "default", "enabled": true}, {"key": "as4_mode", "value": "dummy", "description": "AS4 mode: dummy, production, or test", "type": "default", "enabled": true}, {"key": "security_enabled", "value": "false", "description": "Whether AS4 security is enabled", "type": "default", "enabled": true}, {"key": "validation_enabled", "value": "true", "description": "Whether XML validation is enabled", "type": "default", "enabled": true}, {"key": "default_country", "value": "DE", "description": "Default country code for testing", "type": "default", "enabled": true}, {"key": "default_document_type", "value": "INVOICE", "description": "Default document type for testing", "type": "default", "enabled": true}, {"key": "test_sender_de", "value": "9915:test-sender-de", "description": "German test sender participant ID", "type": "default", "enabled": true}, {"key": "test_receiver_de", "value": "9922:test-receiver-de", "description": "German test receiver participant ID", "type": "default", "enabled": true}, {"key": "test_sender_fr", "value": "9915:test-sender-fr", "description": "French test sender participant ID", "type": "default", "enabled": true}, {"key": "test_receiver_fr", "value": "9922:test-receiver-fr", "description": "French test receiver participant ID", "type": "default", "enabled": true}, {"key": "test_sender_it", "value": "9915:test-sender-it", "description": "Italian test sender participant ID", "type": "default", "enabled": true}, {"key": "test_receiver_it", "value": "9922:test-receiver-it", "description": "Italian test receiver participant ID", "type": "default", "enabled": true}, {"key": "generated_de_invoice_xml", "value": "", "description": "Generated German invoice XML (populated by tests)", "type": "default", "enabled": true}, {"key": "generated_fr_invoice_xml", "value": "", "description": "Generated French invoice XML (populated by tests)", "type": "default", "enabled": true}, {"key": "generated_it_creditnote_xml", "value": "", "description": "Generated Italian credit note XML (populated by tests)", "type": "default", "enabled": true}, {"key": "de_message_id", "value": "", "description": "German AS4 message ID (populated by tests)", "type": "default", "enabled": true}, {"key": "fr_message_id", "value": "", "description": "French AS4 message ID (populated by tests)", "type": "default", "enabled": true}, {"key": "it_message_id", "value": "", "description": "Italian AS4 message ID (populated by tests)", "type": "default", "enabled": true}, {"key": "reverse_flow_response", "value": "", "description": "Reverse flow response XML (populated by tests)", "type": "default", "enabled": true}, {"key": "test_timeout", "value": "30000", "description": "Test timeout in milliseconds", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-15T10:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}