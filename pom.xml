<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.example</groupId>
    <artifactId>phase4</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <phase4.version>2.7.4</phase4.version>
        <spring.boot.version>3.2.0</spring.boot.version>
        <slf4j.version>2.0.9</slf4j.version>
        <httpclient5.version>5.2.3</httpclient5.version>
        <httpcore5.version>5.2.4</httpcore5.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Force compatible Apache HttpClient versions -->
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.core5</groupId>
                <artifactId>httpcore5</artifactId>
                <version>${httpcore5.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.core5</groupId>
                <artifactId>httpcore5-h2</artifactId>
                <version>${httpcore5.version}</version>
            </dependency>
            <!-- Also manage other HttpClient related dependencies -->
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5-fluent</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5-cache</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Jackson for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.2</version>
        </dependency>

        <!-- Bean Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <!-- JPA for database operations -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

        <!-- H2 Database for development -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>2.2.224</version>
            <scope>runtime</scope>
        </dependency>

        <!-- PostgreSQL Database for production -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.6.0</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Lombok for reducing boilerplate code -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>

        <!-- Force SLF4J to use Logback only -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.13</version>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.14</version>
        </dependency>

        <!-- JAXB for XML processing -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>xsd-spring-boot</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.3.8</version>
        </dependency>

        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>4.0.2</version>
        </dependency>


        <!-- Phase4 AS4 Core Dependencies -->
        <dependency>
            <groupId>com.helger.phase4</groupId>
            <artifactId>phase4-lib</artifactId>
            <version>2.7.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <!-- Exclude conflicting HttpClient versions -->
                <exclusion>
                    <groupId>org.apache.httpcomponents.client5</groupId>
                    <artifactId>httpclient5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5-h2</artifactId>
                </exclusion>
                <!-- Exclude ph-httpclient that brings in conflicting versions -->
                <exclusion>
                    <groupId>com.helger.web</groupId>
                    <artifactId>ph-httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.helger.phase4</groupId>
            <artifactId>phase4-peppol-client</artifactId>
            <version>2.7.4</version> <!-- Match with phase4-lib version -->
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <!-- Exclude conflicting HttpClient versions -->
                <exclusion>
                    <groupId>org.apache.httpcomponents.client5</groupId>
                    <artifactId>httpclient5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5-h2</artifactId>
                </exclusion>
                <!-- Exclude ph-httpclient that brings in conflicting versions -->
                <exclusion>
                    <groupId>com.helger.web</groupId>
                    <artifactId>ph-httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.helger.phase4</groupId>
            <artifactId>phase4-profile-peppol</artifactId>
            <version>2.7.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <!-- Exclude conflicting HttpClient versions -->
                <exclusion>
                    <groupId>org.apache.httpcomponents.client5</groupId>
                    <artifactId>httpclient5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5-h2</artifactId>
                </exclusion>
                <!-- Exclude ph-httpclient that brings in conflicting versions -->
                <exclusion>
                    <groupId>com.helger.web</groupId>
                    <artifactId>ph-httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>



        <!-- Spring Boot Dependencies (Optional but recommended) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>${spring.boot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring.boot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- UBL Processing -->
        <dependency>
            <groupId>com.helger.ubl</groupId>
            <artifactId>ph-ubl23</artifactId>
            <version>8.0.0</version>
        </dependency>

        <!-- Jakarta Annotations (for @PostConstruct) -->
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>2.1.1</version>
        </dependency>

        <!-- Saxon for Schematron validation -->
        <dependency>
            <groupId>net.sf.saxon</groupId>
            <artifactId>Saxon-HE</artifactId>
            <version>12.3</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- XML Processing -->
        <dependency>
            <groupId>com.helger.commons</groupId>
            <artifactId>ph-commons</artifactId>
            <version>11.1.5</version>
            <exclusions>
                <!-- Exclude conflicting HttpClient versions -->
                <exclusion>
                    <groupId>org.apache.httpcomponents.client5</groupId>
                    <artifactId>httpclient5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5-h2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Apache HttpClient - Force compatible versions -->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>${httpclient5.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.core5</groupId>
            <artifactId>httpcore5</artifactId>
            <version>${httpcore5.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.core5</groupId>
            <artifactId>httpcore5-h2</artifactId>
            <version>${httpcore5.version}</version>
        </dependency>

        <!-- Helger HttpClient wrapper - compatible with our HttpClient version -->
        <dependency>
            <groupId>com.helger.web</groupId>
            <artifactId>ph-httpclient</artifactId>
            <version>10.1.7</version>
            <exclusions>
                <!-- Exclude its HttpClient dependencies to use our versions -->
                <exclusion>
                    <groupId>org.apache.httpcomponents.client5</groupId>
                    <artifactId>httpclient5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents.core5</groupId>
                    <artifactId>httpcore5-h2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.10.1</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring.boot.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Maven Compiler Plugin with -parameters flag -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <parameters>true</parameters>
                </configuration>
            </plugin>

            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <mainClass>com.morohub.apsp.AS4Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>