<?xml version="1.0" encoding="UTF-8"?><schema xmlns="http://purl.oclc.org/dsdl/schematron" queryBinding="xslt2">
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:slack" as="xs:boolean">
    <xsl:param name="exp" as="xs:decimal"/>
    <xsl:param name="val" as="xs:decimal"/>
    <xsl:param name="slack" as="xs:decimal"/>
    <xsl:value-of select="xs:decimal($exp + $slack) &gt;= $val and xs:decimal($exp - $slack) &lt;= $val"/>
  </xsl:function>
  <ns prefix="ext" uri="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"/>
  <ns prefix="cbc" uri="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"/>
  <ns prefix="cac" uri="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"/>
  <ns prefix="qdt" uri="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDataTypes-2"/>
  <ns prefix="udt" uri="urn:oasis:names:specification:ubl:schema:xsd:UnqualifiedDataTypes-2"/>
  <ns prefix="cn" uri="urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2"/>
  <ns prefix="ubl" uri="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"/>
  <ns prefix="u" uri="utils"/>
  <ns prefix="xs" uri="http://www.w3.org/2001/XMLSchema"/>
  <phase id="PINTmodelaligned_phase">
    <active pattern="UBL-modelaligned"/>
  </phase>
  <phase id="codelistaligned_phase">
    <active pattern="Codesmodelaligned"/>
  </phase>
  <pattern id="UBL-modelaligned">
    <rule context="cac:AccountingCustomerParty/cac:Party">
      <assert id="ibr-010-ae" flag="fatal" test="not(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID) or not(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID = &#34;PAS&#34;) or (cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID=&#34;PAS&#34; and cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyName  and matches(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyName, &#34;^(AF|AX|AL|DZ|AS|AD|AO|AI|AQ|AG|AR|AM|AW|AU|AT|AZ|BS|BH|BD|BB|BY|BE|BZ|BJ|BM|BT|BO|BQ|BA|BW|BV|BR|IO|BN|BG|BF|BI|CV|KH|CM|CA|KY|CF|TD|CL|CN|CX|CC|CO|KM|CG|CD|CK|CR|CI|HR|CU|CW|CY|CZ|DK|DJ|DM|DO|EC|EG|SV|GQ|ER|EE|SZ|ET|FK|FO|FJ|FI|FR|GF|PF|TF|GA|GM|GE|DE|GH|GI|GR|GL|GD|GP|GU|GT|GG|GN|GW|GY|HT|HM|VA|HN|HK|HU|IS|IN|ID|IR|IQ|IE|IM|IL|IT|JM|JP|JE|JO|KZ|KE|KI|KP|KR|KW|KG|LA|LV|LB|LS|LR|LY|LI|LT|LU|MO|MG|MW|MY|MV|ML|MT|MH|MQ|MR|MU|YT|MX|FM|MD|MC|MN|ME|MS|MA|MZ|MM|NA|NR|NP|NL|NC|NZ|NI|NE|NG|NU|NF|MP|NO|OM|PK|PW|PS|PA|PG|PY|PE|PH|PN|PL|PT|PR|QA|MK|RO|RU|RW|RE|BL|SH|KN|LC|MF|PM|VC|WS|SM|ST|SA|SN|RS|SC|SL|SG|SX|SK|SI|SB|SO|ZA|GS|SS|ES|LK|SD|SR|SJ|SE|CH|SY|TW|TJ|TZ|TH|TL|TG|TK|TO|TT|TN|TR|TM|TC|TV|UG|UA|AE|GB|US|UM|UY|UZ|VU|VE|VN|VG|VI|WF|EH|YE|ZM|ZW)$&#34;))">[ibr-010-ae]-Passport issuing country code (BTAE-19) MUST be there when Buyer legal registration identifier type (BTAE-16)  is 'Passport'</assert>
      <assert id="ibr-101-ae" flag="fatal" test="(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID = &#34;TL&#34; and cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyName) or not(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID = &#34;TL&#34;)">[ibr-101-ae]-Authority name (BTAE-11) MUST be there when Buyer legal registration identifier type (BTAE-16) is Commercial/Trade license.</assert>
      <assert id="ibr-136-ae" flag="fatal" test="((cbc:InvoiceTypeCode = &#34;480&#34; or cbc:InvoiceTypeCode = &#34;81&#34;) and cac:AccountingCustomerParty/cac:Party/cac:PartyLegalEntity/cbc:CompanyID) or not(cbc:InvoiceTypeCode = &#34;480&#34; or cbc:InvoiceTypeCode = &#34;81&#34;)">[ibr-136-ae]-Buyer legal registration identifier (IBT-047) must be present when Invoice type code [IBT-003] is 'Out of scope of VAT' or 'Credit note related to goods or services'</assert>
      <assert id="ibr-149-ae" flag="fatal" test="not(cac:Party/cbc:EndpointID/@schemeID = &#34;0235&#34; and cac:Party/cbc:EndpointID != &#34;1XXXXXXXXX&#34; and not(cac:Party/cac:PartyTaxScheme/cbc:CompanyID))">[ibr-149-ae]-The buyer legal registration identifier (IBT-048) MUST be provided when the scheme identifier (IBT-049-1) is '0235' and buyer electronic address (IBT-049) is not '1XXXXXXXXX'</assert>
      <assert id="ibr-179-ae" flag="fatal" test="count(cac:PartyTaxScheme/cbc:CompanyID) &lt;=1">[ibr-179-ae]-Buyer tax identifier (IBT-048) MUST occur maximum once</assert>
      <assert id="ibr-135-ae" flag="fatal" test="((cac:PartyIdentification/cbc:ID or cac:PartyTaxScheme/cbc:CompanyID) and (not(matches(../../cbc:ProfileExecutionID, &#34;^\d{7}1$&#34;)) and cbc:EndpointID/@schemeID = &#34;0235&#34; and not(matches(cbc:EndpointID, &#34;^1\d{9}$&#34;)))) or not(not(matches(../../cbc:ProfileExecutionID, &#34;^\d{7}1$&#34;)) and cbc:EndpointID/@schemeID = &#34;0235&#34; and not(matches(cbc:EndpointID, &#34;^1\d{9}$&#34;)))">[ibr-135-ae]-Either Buyer identifier (IBT-046) or Buyer tax identifier (IBT-048 ) MUST be present when the Invoice transaction type code [BTAE-02]  is other than XXXXXXX1 (Exports) and scheme identifier (IBT-049-1) is '0235' and buyer electronic address (IBT-049) is not '1XXXXXXXXX'</assert>
      <assert id="ibr-180-ae" flag="fatal" test="not(cbc:EndpointID[@schemeID = &#34;0235&#34;] and cac:PartyLegalEntity/cbc:CompanyID) or cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID">[ibr-180-ae]-When Scheme identifier (IBT-049-1) is '0235' and Buyer legal registration identifier (ibt-047) is provided, then Buyer legal registration identifier type (BTAE-16) MUST be present</assert>
    </rule>
    <rule context="cac:DeliveryTerms">
      <assert id="ibr-196-ae" flag="fatal" test="exists(cbc:ID)">[ibr-196-ae]- The Incoterms (BTAE-22) must be provided.</assert>
    </rule>
    <rule context="cac:AccountingCustomerParty/cac:Party/cac:PostalAddress">
      <assert id="ibr-144-ae" flag="fatal" test="(cbc:StreetName) and (cbc:CityName) and (cbc:CountrySubentity)">[ibr-144-ae]-In Buyer postal address (IBG-08), Address line 1 (IBT-050), Buyer city (IBT-052) and Buyer country subdivision (IBT-054) must be provided</assert>
    </rule>
    <rule context="cac:AccountingBuyerParty/cac:Party/cac:PostalAddress">
      <assert id="ibr-143-ae" flag="fatal" test="(cbc:StreetName) and (cbc:CityName) and (cbc:CountrySubentity)">[ibr-143-ae]-In Seller postal address (IBG-05), Seller address line 1 (IBT-035),  Seller city (IBT-037) and Seller to country subdivision (IBT-039) must be provided.</assert>
    </rule>
    <rule context="cac:AccountingSupplierParty/cac:Party">
      <assert id="ibr-012-ae" flag="fatal" test="not(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID) or not(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID = &#34;PAS&#34;) or (cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID=&#34;PAS&#34; and cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyName  and matches(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyName, &#34;^(AF|AX|AL|DZ|AS|AD|AO|AI|AQ|AG|AR|AM|AW|AU|AT|AZ|BS|BH|BD|BB|BY|BE|BZ|BJ|BM|BT|BO|BQ|BA|BW|BV|BR|IO|BN|BG|BF|BI|CV|KH|CM|CA|KY|CF|TD|CL|CN|CX|CC|CO|KM|CG|CD|CK|CR|CI|HR|CU|CW|CY|CZ|DK|DJ|DM|DO|EC|EG|SV|GQ|ER|EE|SZ|ET|FK|FO|FJ|FI|FR|GF|PF|TF|GA|GM|GE|DE|GH|GI|GR|GL|GD|GP|GU|GT|GG|GN|GW|GY|HT|HM|VA|HN|HK|HU|IS|IN|ID|IR|IQ|IE|IM|IL|IT|JM|JP|JE|JO|KZ|KE|KI|KP|KR|KW|KG|LA|LV|LB|LS|LR|LY|LI|LT|LU|MO|MG|MW|MY|MV|ML|MT|MH|MQ|MR|MU|YT|MX|FM|MD|MC|MN|ME|MS|MA|MZ|MM|NA|NR|NP|NL|NC|NZ|NI|NE|NG|NU|NF|MP|NO|OM|PK|PW|PS|PA|PG|PY|PE|PH|PN|PL|PT|PR|QA|MK|RO|RU|RW|RE|BL|SH|KN|LC|MF|PM|VC|WS|SM|ST|SA|SN|RS|SC|SL|SG|SX|SK|SI|SB|SO|ZA|GS|SS|ES|LK|SD|SR|SJ|SE|CH|SY|TW|TJ|TZ|TH|TL|TG|TK|TO|TT|TN|TR|TM|TC|TV|UG|UA|AE|GB|US|UM|UY|UZ|VU|VE|VN|VG|VI|WF|EH|YE|ZM|ZW)$&#34;))">[ibr-012-ae]-Passport issuing country code (BTAE-18) MUST be there when Seller legal registration identifier type (BTAE-15)  is 'Passport'</assert>
      <assert id="ibr-172-ae" flag="fatal" test="(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID = &#34;TL&#34; and (cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyName)) or not(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID = &#34;TL&#34;)">[ibr-172-ae]-Authority name (BTAE-12) MUST be there when the value in Seller legal registration identifier type (BTAE-15) is Commercial/Trade license.</assert>
      <assert id="ibr-177-ae" flag="fatal" test="not(matches(cbc:ProfileExecutionID, &#34;^[01]{5}1[01]{2}$&#34;)) or cac:PartyTaxScheme/cbc:CompanyID != cac:PartyIdentification/cbc:ID">[ibr-177-ae]-Either Seller tax registration identifier (IBT-032) or Seller tax identifier (IBT-031) MUST be provided</assert>
      <assert id="ibr-178-ae" flag="fatal" test="(count(cac:PartyTaxScheme) = 2 and count(cac:PartyTaxScheme[cac:TaxScheme/cbc:ID = &#34;VAT&#34;]) = 1) or count(cac:PartyTaxScheme) &lt; 2">[ibr-178-ae]-Tax scheme code, if provided in (IBT-031-1) shall be '!VAT' when Seller tax registration identifier (IBT-032) is provided</assert>
      <assert id="ibr-150-ae" flag="fatal" test="not(cac:Party/cbc:EndpointID/@schemeID = &#34;0235&#34; and not(cac:Party/cac:PartyLegalEntity/cbc:CompanyID))">[ibr-150-ae]-The Seller legal registration identifier (IBT-030) MUST be provided when the scheme identifier (IBT-034-1) is '0235'</assert>
      <assert id="ibr-173-ae" flag="fatal" test="not(cac:PartyLegalEntity/cbc:CompanyID and  cbc:EndpointID/@schemeID = &#34;0235&#34; and cac:PostalAddress/cac:Country/cbc:IdentificationCode = &#34;AE&#34; and  not(cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID = (&#34;TL&#34;, &#34;EID&#34;, &#34;PAS&#34;, &#34;CD&#34;)))">[ibr-173-ae]-The value in Seller legal registration identifier type [BTAE-15] Scheme identifier [IBT-030-1] should either be 'Commercial/Trade license' or 'Emirates ID' or 'Passport' or 'Cabinet decision' when the value in Seller legal registration identifier (IBT-030) is provided and scheme identifier (IBT-034-1) is '0235' and the Seller country code (IBT-055) is AE.</assert>
      <assert id="ibr-181-ae" flag="fatal" test="not(cbc:EndpointID[@schemeID = &#34;0235&#34;]) or not(cac:PartyLegalEntity/cbc:CompanyID) or  cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID">[ibr-181-ae]-When Scheme identifier (IBT-034-1) is '0235' and Seller legal registration identifier (ibt-030) is provided, then Seller legal registration identifier type (BTAE-15) MUST be present</assert>
    </rule>
    <rule context="cac:Price/cac:AllowanceCharge">
      <assert id="aligned-ibrp-004" flag="fatal" test="not(cbc:BaseAmount) or xs:decimal(../cbc:PriceAmount) = xs:decimal(cbc:BaseAmount) - xs:decimal(cbc:Amount)">[aligned-ibrp-004]-Item net price (ibt-146) MUST equal (Gross price (ibt-148) - Price discount (ibt-147)) when gross price is provided.</assert>
    </rule>
    <rule context="cac:Price">
      <assert id="ibr-126-ae" flag="fatal" test="boolean(cbc:BaseQuantity) and boolean(cac:AllowanceCharge/cbc:BaseAmount)">[ibr-126-ae]-In Price Details (IBG-29), Item price base quantity (IBT-149) and Item Gross Price (IBT-148) MUST be there.</assert>
    </rule>
    <rule context="cac:PaymentMeans">
      <assert id="ibr-192-ae" flag="fatal" test="not(cbc:PaymentMeansTypeCode = &#34;30&#34;) or cac:PayeeFinancialAccount/cbc:ID">[ibr-192-ae]- when Payment means type code (ibt-081) is 'credit transfer' then Payment account identifier (ibt-084) must be provided.</assert>
    </rule>
    <rule context="cac:PostalAddress">
      <assert id="ibr-128-ae" flag="fatal" test="(cac:Country/cbc:IdentificationCode=&#34;AE&#34; and cbc:CountrySubentity = (&#34;AUH&#34;, &#34;DXB&#34;, &#34;SHJ&#34;, &#34;UAQ&#34;, &#34;FUJ&#34;, &#34;AJM&#34;, &#34;RAK&#34;)) or not(cac:Country/cbc:IdentificationCode=&#34;AE&#34;)">[ibr-128-ae]-When Country code (IBT-040, IBT-055, IBT-069, IBT-080) is AE, then country subdivision (IBT-039, IBT-054, IBT-068, IBT-079) should be one among one of these (AUH, DXB, SHJ, UAQ, FUJ, AJM, RAK).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator = false()]">
      <assert id="aligned-ibrp-032" flag="fatal" test="not(parent::ubl:Invoice|parent::cn:CreditNote) or exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID)">[aligned-ibrp-032]-Each Document level allowance (ibg-20) MUST have a Document level allowance VAT category code (ibt-095).</assert>
      <assert id="aligned-ibrp-057" flag="fatal" test="not(cbc:MultiplierFactorNumeric or cbc:BaseAmount) or (cbc:MultiplierFactorNumeric and cbc:BaseAmount)">[aligned-ibrp-057]-Either both or neither Allowance base amount (ibt-093) and percentage (ibt-094) MUST be provided.</assert>
      <assert id="ibr-131-ae" flag="fatal" test="not(exists(cbc:BaseAmount) and exists(cbc:MultiplierFactorNumeric)) or (cbc:Amount = round(cbc:BaseAmount * cbc:MultiplierFactorNumeric) div 100)">[ibr-131-ae]-Allowance amount (IBT-092, IBT-136) must equal base amount (IBT-093, IBT-137) * percentage (IBT-094, IBT-138) /100 if base amount and percentage exists</assert>
      <assert id="ibr-115-ae" flag="fatal" test="not(cac:CategoryCode/cbc:ID = &#34;N&#34;)">[ibr-115-ae]-[ibr-115-ae]-Document level allowance tax category code (IBT-095) cannot be 'Standard rate additional VAT'.</assert>
      <assert id="ibr-168-ae" flag="fatal" test="not(cac:TaxCategory/cbc:ID = &#34;E&#34; and not(exists(cbc:AllowanceChargeReasonCode)))">[ibr-168-ae]-Document level allowances (IBG-20) with Document level allowance VAT category code (IBT-095) as 'Exempt from VAT' MUST have a Document level allowance VAT exemption reason code (IBT-196)</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator = true()]">
      <assert id="aligned-ibrp-037" flag="fatal" test="not(parent::ubl:Invoice|parent::cn:CreditNote) or exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID)">[aligned-ibrp-037]-Each Document level charge (ibg-21) MUST have a Document level charge VAT category code (ibt-102).</assert>
      <assert id="aligned-ibrp-058" flag="fatal" test="not(cbc:MultiplierFactorNumeric or cbc:BaseAmount) or (cbc:MultiplierFactorNumeric and cbc:BaseAmount)">[aligned-ibrp-058]-Either both or neither Charge base amount (ibt-100) and percentage (ibt-101) MUST be provided.</assert>
      <assert id="ibr-146-ae" flag="fatal" test="not(exists(cbc:BaseAmount) and exists(cbc:MultiplierFactorNumeric)) or (cbc:Amount = (cbc:BaseAmount * cbc:MultiplierFactorNumeric) div 100)">[ibr-146-ae]-Charge amount (IBT-099, IBT-141) must equal base amount (IBT-100, IBT-142) * percentage (IBT-101, IBT-143) /100 if base amount and percentage exists</assert>
      <assert id="ibr-114-ae" flag="fatal" test="not(cac:CategoryCode/cbc:ID = &#34;N&#34;)">[ibr-114-ae]-Document level charge VAT category code (IBT-102) cannot be 'Standard rate additional VAT'.</assert>
      <assert id="ibr-169-ae" flag="fatal" test="not(cac:TaxCategory/cbc:ID = &#34;E&#34; and not(exists(cbc:AllowanceChargeReasonCode)))">[ibr-169-ae]-Document level charge (IBG-21) with Document level charge VAT category code (IBT-102) as 'Exempt from VAT' MUST have a Document level charge VAT exemption reason code (IBT-198).</assert>
    </rule>
    <rule context="cbc:CalculationRate">
      <assert id="ibr-002-ae" flag="fatal" test="not(matches(., &#34;^\d+(\.\d{7})?$&#34;))">[ibr-002-ae]-Currency exchange rate [BTAE-04] should contain the values till maximum of 6 decimal places.</assert>
    </rule>
    <rule context="cac:InvoiceLine | cac:CreditNoteLine">
      <assert id="ibr-104-ae" flag="fatal" test="not(exists(cac:Item/cac:ClassifiedTaxCategory)) or (exists(cac:Item/cac:ClassifiedTaxCategory) and exists(cac:ItemPriceExtension/cac:TaxTotal/cbc:TaxAmount) and exists(cac:ItemPriceExtension/cbc:Amount))">[ibr-104-ae]-An Invoice line (IBG-25), where Line VAT Information (IBG-30) is present then Invoice line amount in AED (BTAE-10) and VAT Line amount in AED (BTAE-08) must be provided.</assert>
      <assert id="ibr-111-ae" flag="fatal" test="(cac:Item/cac:ClassifiedTaxCategory/cbc:ID=&#34;N&#34; and cac:Item/cac:ClassifiedTaxCategory/cbc:Percent &gt; 0) or not(cac:Item/cac:ClassifiedTaxCategory/cbc:ID=&#34;N&#34;)">[ibr-111-ae]-An Invoice line (IBG-25) where the VAT category code (IBT-151) is 'Standard rate additional VAT' the Invoiced item VAT rate (IBT-152) should not be zero.</assert>
      <assert id="ibr-145-ae" flag="fatal" test="(cac:Item/cac:ClassifiedTaxCategory)">[ibr-145-ae]-Each Invoice line (IBG-25) MUST be categorized with an Invoiced item tax category code (IBT-151)..</assert>
      <assert id="ibr-147-ae" flag="fatal" test="round(cbc:LineExtensionAmount * 100) div 100 = round((((cbc:InvoicedQuantity | cbc:CreditedQuantity) * (cac:Price/cbc:PriceAmount div cac:Price/cbc:BaseQuantity)) + sum(cac:AllowanceCharge[cbc:ChargeIndicator=&#34;true&#34;]/cbc:Amount) - sum(cac:AllowanceCharge[cbc:ChargeIndicator=&#34;false&#34;]/cbc:Amount)) * 100) div 100">[ibr-147-ae]-Invoice line net amount (IBT-131) MUST equal (Invoiced quantity (IBT-129) * (Item net price (IBT-146)/item price base quantity (IBT-149)) + Sum of invoice line charge amount (IBT-141) - sum of invoice line allowance amount (IBT-136).</assert>
      <assert id="ibr-167-ae" flag="fatal" test="not(cac:Item/cac:ClassifiedTaxCategory/cbc:ID = &#34;E&#34; and not(exists(cac:Item/cac:ClassifiedTaxCategory/cbc:TaxExemptionReasonCode)))">[ibr-167-ae]-Line VAT information (IBG-30) with Invoiced item VAT category code (IBT-151) as 'Exempt from VAT' MUST have a VAT exemption reason code (IBT-186).</assert>
      <assert id="ibr-123-ae" flag="fatal" test="((../cbc:InvoiceTypeCode = &#34;81&#34; or ../cbc:InvoiceTypeCode = &#34;480&#34;) and count(cac:Item/cac:ClassifiedTaxCategory) = 1) or not(../cbc:InvoiceTypeCode = &#34;81&#34; or ../cbc:InvoiceTypeCode = &#34;480&#34;)">[ibr-123-ae]- Line VAT Information (IBG - 30) MUST be there and can occur maximum once except in case when invoice type code is 'Out of scope of VAT' or 'Credit note related to goods or services'.</assert>
      <assert id="ibr-194-ae" flag="fatal" test="exists(cac:ItemPriceExtension/cbc:Amount)">[ibr-194-ae]- Invoice line Amount payable (BTAE-10) must be provided.</assert>
    </rule>
    <rule context="/ubl:Invoice | /cn:CreditNote">
      <assert id="aligned-ibrp-001-ae" flag="fatal" test="starts-with(normalize-space(cbc:CustomizationID/text()), 'urn:peppol:pint:billing-1@ae-1') or starts-with(normalize-space(cbc:CustomizationID/text()), 'urn:peppol:pint:selfbilling-1@ae-1')">[aligned-ibrp-001-ae]-Specification identifier (ibt-024) MUST start with the value 'urn:peppol:pint:billing-1@ae-1' or 'urn:peppol:pint:selfbilling-1@ae-1'.</assert>
      <assert id="aligned-ibrp-002-ae" flag="fatal" test="/*/cbc:ProfileID and (matches(normalize-space(/*/cbc:ProfileID), 'urn:peppol:bis:billing') or matches(normalize-space(/*/cbc:ProfileID), 'urn:peppol:bis:selfbilling'))">[aligned-ibrp-002-ae]-Business process (ibt-023) MUST be in the format 'urn:peppol:bis:billing' or 'urn:peppol:bis:selfbilling'.</assert>
      <assert id="aligned-ibrp-e-01" flag="fatal" test="((exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'E']) or exists(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'E'])) and (count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'E']) = 1)) or (not(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'E']) and not(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'E']))">[aligned-ibrp-e-01]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the VAT category code (ibt-151, ibt-95 or ibt-102) is "Exempt from VAT" MUST contain exactly one VAT breakdown (ibg-23) with the VAT category code (ibt-118) equal to "Exempt from VAT".</assert>
      <assert id="aligned-ibrp-o-01" flag="fatal" test="((exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'O']) or exists(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'O'])) and (count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'O']) = 1)) or (not(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'O']) and not(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'O']))">[aligned-ibrp-o-01]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the VAT category code (ibt-151, ibt-95 or ibt-102) is "Not subject to VAT" MUST contain exactly one VAT breakdown group (ibg-23) with the VAT category code (ibt-118) equal to "Not subject to VAT".</assert>
      <assert id="aligned-ibrp-s-01" flag="fatal" test="((count(//cac:AllowanceCharge/cac:TaxCategory[normalize-space(cbc:ID) = 'S']) + count(//cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'S'])) &gt; 0 and count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'S']) &gt; 0) or ((count(//cac:AllowanceCharge/cac:TaxCategory[normalize-space(cbc:ID) = 'S']) + count(//cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'S'])) = 0 and count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'S']) = 0)">[aligned-ibrp-s-01]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the VAT category code (ibt-151, ibt-95 or ibt-102) is "Standard rated" MUST contain in the VAT breakdown (ibg-23) at least one VAT category code (ibt-118) equal with "Standard rated".</assert>
      <assert id="aligned-ibrp-z-01" flag="fatal" test="((exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'Z']) or exists(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'Z'])) and (count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'Z']) = 1)) or (not(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'Z']) and not(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'Z']))">[aligned-ibrp-z-01]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the VAT category code (ibt-151, ibt-95 or ibt-102) is "Zero rated" MUST contain in the VAT breakdown (ibg-23) exactly one VAT category code (ibt-118) equal with "Zero rated".</assert>
      <assert id="aligned-ibrp-ae-01-ae" flag="fatal" test="((exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'AE']) or exists(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'AE'])) and (count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'AE']) &gt;= 1)) or (not(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'AE']) and not(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID[normalize-space(.) = 'AE']))">[aligned-ibrp-ae-01-ae]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the VAT category code (ibt-151, ibt-095 or ibt-102) is "Reverse charge" MUST contain in the VAT Breakdown (ibg-23) at least  one VAT category code (ibt-118) equal with "VAT reverse charge".</assert>
      <assert id="aligned-ibrp-sr-12" flag="fatal" test="(count(cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme[cac:TaxScheme/upper-case(cbc:ID)='VAT']/cbc:CompanyID) &lt;= 1)">[aligned-ibrp-sr-12]-Seller tax identifier (ibt-031) MUST occur maximum once</assert>
      <assert id="ibr-007-ae" flag="fatal" test="not(matches(cbc:ProfileExecutionID, &#34;^1[01]{6}$&#34;)) or cac:BuyerCustomerParty/cac:Party/cac:PartyIdentification/cbc:ID">[ibr-007-ae]-When Invoice Transaction-type code (BTAE-02) has value 1XXXXXXX (Free trade zone), then providing value in Beneficiary ID (BTAE-01) MUST be provided.</assert>
      <assert id="ibr-134-ae" flag="fatal" test="(not(cbc:InvoiceTypeCode = &#34;480&#34; or cbc:InvoiceTypeCode = &#34;81&#34;) and cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme[cac:TaxScheme/cbc:ID = &#34;VAT&#34;]/cbc:CompanyID) or (cbc:InvoiceTypeCode = &#34;480&#34; or cbc:InvoiceTypeCode = &#34;81&#34;)">[ibr-134-ae]-Seller tax Identifier (IBT-031) MUST be there, except when the Invoice type code (IBT-003) is 'Out of scope of VAT' or 'Credit note related to goods or services'.</assert>
      <assert id="ibr-137-ae" flag="fatal" test="(matches(cbc:ProfileExecutionID, &#34;^[01]{5}1[01]{2}$&#34;) and cac:SellerSupplierParty/cac:Party/cac:PartyIdentification/cbc:ID) or not(matches(cbc:ProfileExecutionID, &#34;^[01]{5}1[01]{2}$&#34;))">[ibr-137-ae]-Principle ID (BTAE-14) is MUST, where Invoice transaction type code [BT-UAE-002] is XXXXX1XX (Disclosed Agent billing).</assert>
      <assert id="ibr-138-ae" flag="fatal" test="(matches(cbc:ProfileExecutionID, &#34;^[01]{3}1[01]{4}$&#34;) and cac:InvoicePeriod) or not(matches(cbc:ProfileExecutionID, &#34;^[01]{3}1[01]{4}$&#34;))">[ibr-138-ae]-Invoicing period [IBG-14] is MUST, where Invoice transaction type code [BTAE-002] is XXX1XXXX (Summary invoice).</assert>
      <assert id="ibr-141-ae" flag="fatal" test="not(cbc:TaxPointDate) or cbc:TaxPointDate &lt; cbc:IssueDate">[ibr-141-ae]-When, Tax point date [IBT-007] is present, it should be before the Invoice issue date [IBT-002].</assert>
      <assert id="ibr-055-ae" flag="fatal" test="((cbc:InvoiceTypeCode = &#34;381&#34; or cbc:InvoiceTypeCode = &#34;81&#34;) and (((cac:BillingReference) and  (cac:DiscrepancyResponse/cbc:ResponseCode != &#34;VD&#34;)) or (not(cac:BillingReference) and  (cac:DiscrepancyResponse/cbc:ResponseCode = &#34;VD&#34;))))  or not(cbc:InvoiceTypeCode = &#34;381&#34; or cbc:InvoiceTypeCode = &#34;81&#34;)">[ibr-055-ae]-Preceding invoice reference (IBG-03) is must when invoice type code (IBT-003) is 381 (Credit note) or 81 (Credit note related to goods or services) except when the [BTAE-03] Credit note reason code is 'VD'.</assert>
      <assert id="ibr-142-ae" flag="fatal" test="not(matches(cbc:ProfileExecutionID, &#34;^[01]{6}1[01]$&#34;)) or (cac:Delivery/cac:DeliveryLocation/cac:Address/cbc:StreetName and cac:Delivery/cac:DeliveryLocation/cac:Address/cbc:CityName and  cac:Delivery/cac:DeliveryLocation/cac:Address/cbc:CountrySubentity)">[ibr-142-ae]-In Delivery Information (ibg-13), Deliver to address line 1 (IBT-075), Deliver to city (IBT-077), Deliver to country subdivision (IBT-079) MUST be there, in case the Invoice transaction type code [BTAE-02] is XXXXXX1X (E-commerce supplies).</assert>
      <assert id="ibr-105-ae" flag="fatal" test="(exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))=&#34;VAT&#34;]/cbc:ID[normalize-space(.) = &#34;N&#34;]) and (count(//cac:TaxTotal/cac:TaxSubtotal) = 1)) or not(exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))=&#34;VAT&#34;]/cbc:ID[normalize-space(.) = &#34;N&#34;]))">[ibr-105-ae]-An Invoice that contains an Invoice line (IBG-25), where the VAT category code (IBT-151) is 'Standard rate additional VAT' shall contain exactly one VAT breakdown group (IBG-23) with the VAT category code (IBT-118) equal to 'Standard rate additional VAT'.</assert>
      <assert id="ibr-122-ae" flag="fatal" test="((cbc:InvoiceTypeCode = &#34;81&#34; or cbc:InvoiceTypeCode = &#34;480&#34;) and (//cac:TaxCategory/cbc:ID = &#34;E&#34; or //cac:TaxCategory/cbc:ID = &#34;O&#34; or //cac:TaxCategory/cbc:ID = &#34;Z&#34;) and (//cac:ClassifiedTaxCategory/cbc:ID = &#34;E&#34; or //cac:ClassifiedTaxCategory/cbc:ID = &#34;O&#34;  or //cac:ClassifiedTaxCategory/cbc:ID = &#34;Z&#34;)) or not(cbc:InvoiceTypeCode = &#34;81&#34; or cbc:InvoiceTypeCode = &#34;480&#34;)">[ibr-122-ae]-When Invoice type code (IBT-003) is 'Out of scope of VAT' or 'Credit note related to goods or services', then the Document level allowance VAT category code (IBT-095), Document level charge VAT category code (IBT-102), Invoiced item VAT category code (IBT-151) MUST be either 'Exempt from VAT' and/or 'Not subject to VAT' and/or 'Zero rated'.</assert>
      <assert id="ibr-124-ae" flag="fatal" test="not(cbc:InvoiceTypeCode = &#34;381&#34; or cbc:InvoiceTypeCode = &#34;81&#34;) or not(cbc:TaxPointDate)">[ibr-124-ae]-Tax point date [IBT-007] MUST not be there when invoice type code (IBT-003) is 'credit note' or 'Credit note related to goods or services'.</assert>
      <assert id="ibr-127-ae" flag="fatal" test="((cbc:InvoiceTypeCode | cbc:CreditNoteTypeCode) = &#34;381&#34; or (cbc:InvoiceTypeCode | cbc:CreditNoteTypeCode) = &#34;81&#34; or (cbc:CreditNoteTypeCode) = &#34;261&#34;) or (matches(cbc:ProfileExecutionID, &#34;^[01]1[01]{6}$&#34;)) or (cac:LegalMonetaryTotal/cbc:PayableAmount &gt; 0 and (cbc:DueDate))">[ibr-127-ae]-Payment due date [IBT-009] MUST be present when the amount due for payment (IBT-115) greater than 0, except when invoice type code (IBT-003) is 'Credit note' or 'Credit note related to goods or services' or Invoice transaction-type code (BTAE-002) is X1XXXXXX (Deemed supply) .</assert>
      <assert id="ibr-116-ae" flag="fatal" test="not(matches(cbc:ProfileExecutionID, &#34;^[01]{2}1[01]{5}$&#34;)) or not(//cac:TaxCategoryCode/cbc:ID = &#34;N&#34;)">[ibr-116-ae]-When Invoice transaction-type code (BTAE-02) has value XX1XXXXX (Margin scheme), then the tax category code (IBT-151) should have 'Standard rate additional VAT '.</assert>
      <assert id="ibr-151-ae" flag="fatal" test="not((cbc:InvoiceTypeCode = &#34;380&#34; or cbc:InvoiceTypeCode = &#34;381&#34;) and not(cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory/cbc:ID != &#34;E&#34; and cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory/cbc:ID != &#34;O&#34;))">[ibr-151-ae]-When Invoice type code (IBT-003) is 'Commercial invoice' or 'Credit note',  Invoiced item VAT category code (IBT-151) should not only contain 'Exempt from VAT' and/or 'Not subject to VAT'.</assert>
      <assert id="ibr-152-ae" flag="fatal" test="not(matches(cbc:ProfileExecutionID, &#34;^.{7}1$&#34;)) or (cac:Delivery/cac:DeliveryLocation/cac:Address/cbc:StreetName and cac:Delivery/cac:DeliveryLocation/cac:Address/cbc:CityName and  cac:Delivery/cac:DeliveryLocation/cac:Address/cbc:CountrySubentity)">[ibr-152-ae]-In Delivery Information (IBG-13), Deliver to address line 1 (IBT-075), deliver to city (IBT-077), deliver to country subdivision (IBT-079) MUST be there, in case the Invoice transaction type code [BTAE-002] is XXXXXXX1 (Exports) and the deliver to country code [IBT-080] should not be 'AE'.</assert>
      <assert id="ibr-153-ae" flag="fatal" test="not(cbc:TaxCurrencyCode = &#34;AED&#34; and cbc:DocumentCurrencyCode != &#34;AED&#34; and (not(cac:PricingExchangeRate/cbc:SourceCurrencyCode = cbc:DocumentCurrencyCode) or not(cac:PricingExchangeRate/cbc:TargetCurrencyCode = cbc:TaxCurrencyCode) or not(cac:PricingExchangeRate/cbc:CalculationRate)))">[ibr-153-ae] When the VAT accounting currency (IBT-006) is set to AED and the invoice currency code (IBT-005) differs from AED, the source currency must be designated as the invoice currency code (IBT-005), and the target currency must be specified as the VAT accounting currency (IBT-006), provided that the currency exchange rate (BTAE-04) is available.</assert>
      <assert id="ibr-157-ae" flag="fatal" test="not((cbc:InvoiceTypeCode = &#34;480&#34; or cbc:InvoiceTypeCode = &#34;81&#34;) and  matches(cbc:ProfileExecutionID, &#34;^[01]{2}1[01]{5}$|^[01]1[01]{6}$|^[01]{3}1[01]{4}$&#34;))">[ibr-157-ae]-Invoice Transaction-type code (BTAE-02) cannot be XXX1XXXX (Summary invoice) or X1XXXXXX (Deemed supply) or XX1XXXXX (Margin scheme) when the invoice type code (IBT-003) is 'Out of scope of VAT' or 'Credit note related to goods or services'.</assert>
      <assert id="ibr-158-ae" flag="fatal" test="not(cbc:InvoiceTypeCode = &#34;381&#34; and not(exists(cac:DiscrepancyResponse/cbc:ResponseCode)))">[ibr-158-ae]-Where the Invoice type code [IBT-003] is 'Credit note', Credit note reason code [BTAE-03] MUST be there .</assert>
      <assert id="ibr-159-ae" flag="fatal" test="(cbc:DocumentCurrencyCode != &#34;AED&#34; and (exists(//cbc:CalculationRate))) or cbc:DocumentCurrencyCode = &#34;AED&#34;">[ibr-159-ae]-Currency exchange rate [BTAE-04] is MUST when then Invoice currency code [IBT-005] is different from 'AED'.</assert>
      <assert id="ibr-160-ae" flag="fatal" test="not(cac:InvoicePeriod/cbc:DescriptionCode = &#34;OTH&#34; and not(exists(cbc:Note)))">[ibr-160-ae]-When Frequency of billing (BTAE-06) value is 'Others', then value should be provided in Invoice note (IBT-022).</assert>
      <assert id="ibr-175-ae" flag="fatal" test="cbc:DocumentCurrencyCode = &#34;AED&#34; or (cbc:DocumentCurrencyCode != &#34;AED&#34; and cbc:TaxCurrencyCode = &#34;AED&#34; and exists(cac:TaxTotal/cbc:TaxAmount[@currencyID = &#34;AED&#34;]) and exists(cac:AdditionalDocumentReference[cbc:DocumentTypeCode = &#34;aedtotal-incl-vat&#34;]/cbc:DocumentDescription))">[ibr-175-ae]-When Invoice currency code [IBT-005] is other than 'AED' and Tax accounting currency [IBT-006] is 'AED', then the value in Invoice total VAT amount in VAT accounting currency [IBT-111] and Invoice total amount with VAT in tax accounting currency [BTAE-20] MUST be present.</assert>
      <assert id="ibr-176-ae" flag="fatal" test="(matches(cbc:ProfileExecutionID, &#34;^[01]{5}1[01]{2}$&#34;) and cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme/cbc:CompanyID !=  cac:SellerSupplierParty/cac:Party/cac:PartyIdentification/cbc:ID) or not(matches(cbc:ProfileExecutionID, &#34;^[01]{5}1[01]{2}$&#34;))">[ibr-176-ae]-When Invoice transaction type code [BTAE-02] is XXXXX1XX (Disclosed agent billing), then the value in field Seller tax Identifier [IBT-031] and Principle ID [BTAE-14] should not be the same.</assert>
      <assert id="ibr-183-ae" flag="fatal" test="not(cac:PartyLegalEntity/cbc:CompanyID) or not(cbc:EndpointID[@schemeID = &#34;0235&#34;]) or  starts-with(cbc:EndpointID, &#34;1&#34;) or cac:PartyLegalEntity/cbc:CompanyID/@schemeAgencyID = (&#34;CL&#34;, &#34;EID&#34;, &#34;PAS&#34;, &#34;CD&#34;)">[ibr-183-ae]-The value in Buyer legal registration identifier type [BTAE-16] should either be 'Commercial/Trade license' or 'Emirates ID' or 'Passport' or 'Cabinet decision' when Buyer legal registration identifier (IBT-047) is provided and scheme identifier (IBT-049-1) is '0235' and buyer electronic address (IBT-049) is not '1XXXXXXXXX'.</assert>
      <assert id="ibr-190-ae" flag="fatal" test="not(//cac:TaxCategory/cbc:ID = &#34;S&#34;) or //cac:TaxCategory[cbc:ID = &#34;S&#34;]/cbc:Percent = 5.00">[ibr-190-ae]- When the Invoiced item VAT category code (ibt-151), Document level allowance VAT category code (ibt-095), Document level charge VAT category code (ibt-102) is Standard rated then Invoiced item VAT rate (ibt-152), Document level allowance VAT rate (ibt-096), Document level charge VAT rate (ibt-103) must be 5.00.</assert>
      <assert id="ibr-191-ae" flag="fatal" test="exists(cac:PaymentMeans/cbc:PaymentMeansCode) = not((cbc:InvoiceTypeCode|cbc:CreditNoteTypeCode) = &#34;81&#34; or (cbc:InvoiceTypeCode|cbc:CreditNoteTypeCode) = &#34;381&#34; or  (cbc:InvoiceTypeCode|cbc:CreditNoteTypeCode) = &#34;261&#34;or matches(cbc:ProfileExecutionID, &#34;^\d1\d{6}$&#34;))">[ibr-191-ae]- Payment means type code (ibt-081) must be provided except when the invoice type code (ibt-003) is 'Credit note' or 'Credit note related to goods or Invoice transaction-type code (BTAE-002) is X1XXXXXX (Deemed supply) .</assert>
      <assert id="ibr-154-ae" flag="fatal" test="string-length(cbc:ProfileExecutionID) &lt;= 8 and (matches(cbc:ProfileExecutionID, &#34;^[01]{1,8}$&#34;))">[ibr-154-ae]-Invoice Transaction-type code (BTAE-02) must be provided from the Invoice Transaction Type Code List. It should be a string consisting of no more than 8 characters, exclusively comprising of 0 and 1. The value in this field should be based on the sequence of transaction present in the invoice (as per list order), If applicable '1', and if not applicable '0' .</assert>
      <assert id="ibr-140-ae" flag="fatal" test="not(cbc:TaxCurrencyCode) or cbc:TaxCurrencyCode = &#34;AED&#34;">[ibr-140-ae]-When VAT accounting currency (IBT-006) is present, it shall be AED.</assert>
      <assert id="ibr-193-ae" flag="fatal" test="exists(cbc:UUID)">[ibr-193-ae]- The unique identifier number (BTAE-07) must be provided.</assert>
    </rule>
    <rule context="cac:Item">
      <assert id="ibr-125-ae" flag="fatal" test="boolean(cbc:Description)">[ibr-125-ae]-In Item Information(IBG-31), Item description (IBT-154) MUST be there.</assert>
      <assert id="ibr-166-ae" flag="fatal" test="not(cac:ClassifiedTaxCategory/cbc:ID = &#34;AE&#34; and (exists(cac:InvoiceLine/cac:Item/cac:CommodityClassification/cbc:NatureCode))) or cac:ClassifiedTaxCategory/cbc:ID != &#34;AE&#34;">[ibr-166-ae]-In Item information (IBG-31) where Invoiced VAT category code (IBT-151) is 'Reverse charge', Type of goods or services (BTAE-09) MUST be there.</assert>
      <assert id="ibr-184-ae" flag="fatal" test="not(cac:CommodityClassification/cbc:CommodityCode = &#34;G&#34;) or cac:CommodityClassification/cbc:ItemClassificationCode">[ibr-184-ae]-When the Item type [BTAE-13] is 'Goods' then Item classification identifier (ibt-158) must be provided. .</assert>
      <assert id="ibr-185-ae" flag="fatal" test="not(cac:CommodityClassification/cbc:CommodityCode = &#34;S&#34;) or cac:AdditionalItemIdentification/cbc:ID">[ibr-185-ae]-When the Item type [BTAE-13] is 'Services' then Service accounting code (BTAE-17) must be provided. .</assert>
      <assert id="ibr-186-ae" flag="fatal" test="not(cac:CommodityClassification/cbc:CommodityCode = &#34;B&#34;) or cac:CommodityClassification/cbc:ItemClassificationCode or cac:AdditionalItemIdentification/cbc:ID">[ibr-186-ae]-When the Item type [BTAE-13] is 'Both' then Item classification identifier (ibt-158) and Service accounting code (BTAE-17) must be provided. .</assert>
      <assert id="ibr-187-ae" flag="fatal" test="true()">[ibr-187-ae]- The minimum number of digits to be provided should be 'X' in Item classification identifier (ibt-158) and Service accounting code (BTAE-17).</assert>
      <assert id="ibr-188-ae" flag="fatal" test="not(cac:CommodityClassification/cbc:ItemClassificationCode) or cac:CommodityClassification/cbc:ItemClassificationCode/@listID = &#34;HS&#34;">[ibr-188-ae]- The scheme identifier (ibt-158-1) MUST be HS when Item classification identifier (ibt-158) is provided.</assert>
      <assert id="ibr-189-ae" flag="fatal" test="not(cac:AdditionalItemIdentification/cbc:ID) or cac:AdditionalItemIdentification/cbc:ID/@schemeID = &#34;SAC&#34;">[ibr-189-ae]- The scheme identifier (BTAE-17-1) MUST be SAC when Service accounting code (BTAE-17) is provided.</assert>
    </rule>
    <rule context="cac:TaxSubtotal">
      <assert id="aligned-ibrp-045" flag="fatal" test="exists(cbc:TaxableAmount)">[aligned-ibrp-045]-Each VAT breakdown (ibg-23) MUST have a VAT category taxable amount (ibt-116).</assert>
      <assert id="aligned-ibrp-046" flag="fatal" test="exists(cbc:TaxAmount)">[aligned-ibrp-046]-Each VAT breakdown (ibg-23) MUST have a VAT category tax amount (ibt-117).</assert>
      <assert id="aligned-ibrp-047" flag="fatal" test="exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:ID)">[aligned-ibrp-047]-Each VAT breakdown (ibg-23) MUST be defined through a VAT category code (ibt-118).</assert>
      <assert id="aligned-ibrp-048" flag="fatal" test="exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:Percent) or (cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/normalize-space(cbc:ID)='O')">[aligned-ibrp-048]-Each VAT breakdown (ibg-23) MUST have a VAT category rate (ibt-119), except if the Invoice is not subject to VAT.</assert>
      <assert id="ibr-119-ae" flag="fatal" test="(cac:TaxCategory/cbc:ID = &#34;O&#34;) and not(cac:TaxCategory/cbc:Percent) or  (not(cac:TaxCategory/cbc:ID = &#34;O&#34;)) and cac:TaxCategory/cbc:Percent">[ibr-119-ae]-Each VAT breakdown (IBG-23) shall have a VAT category rate (IBT-119), except if the Invoice is not subject to VAT.</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-s-05" flag="fatal" test="(xs:decimal(cbc:Percent) &gt; 0)">[aligned-ibrp-s-05]-In an Invoice line (ibg-25) where the Invoiced item VAT category code (ibt-151) is "Standard rated" the Invoiced item VAT rate (ibt-152) MUST be greater than zero.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-s-06" flag="fatal" test="(xs:decimal(cbc:Percent) &gt; 0)">[aligned-ibrp-s-06]-In a Document level allowance (ibg-20) where the Document level allowance VAT category code (ibt-95) is "Standard rated" the Document level allowance VAT rate (ibt-96) MUST be greater than zero.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-s-07" flag="fatal" test="(xs:decimal(cbc:Percent) &gt; 0)">[aligned-ibrp-s-07]-In a Document level charge (ibg-21) where the Document level charge VAT category code (ibt-102) is "Standard rated" the Document level charge VAT rate (ibt-103) MUST be greater than zero.</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-s-08" flag="fatal" test="every $rate in xs:decimal(cbc:Percent) satisfies (((exists(//cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID) = 'S'][cac:Item/cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]) or exists(//cac:AllowanceCharge[cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate])) and (u:slack(../xs:decimal(cbc:TaxableAmount), sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='S'][cac:Item/ cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate]/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate]/xs:decimal(cbc:Amount)),0.02))) or ((exists(//cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID) = 'S'][cac:Item/cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]) or exists(//cac:AllowanceCharge[cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate])) and (u:slack(../xs:decimal(cbc:TaxableAmount), sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='S'][cac:Item/cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate]/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate]/xs:decimal(cbc:Amount)),0.02))))">[aligned-ibrp-s-08]-For each different value of VAT category rate (ibt-119) where the VAT category code (ibt-118) is "Standard rated", the VAT category taxable amount (ibt-116) in a VAT breakdown (ibg-23) MUST equal the sum of Invoice line net amounts (ibt-131) plus the sum of document level charge amounts (ibt-99) minus the sum of document level allowance amounts (ibt-92) where the VAT category code (ibt-151, ibt-102, ibt-95) is "Standard rated" and the VAT rate (ibt-152, ibt-103, ibt-96) equals the VAT category rate (ibt-119).</assert>
      <assert id="aligned-ibrp-s-09" flag="fatal" test="u:slack(abs(xs:decimal(../cbc:TaxAmount)) , round((abs(xs:decimal(../cbc:TaxableAmount)) * (xs:decimal(cbc:Percent) div 100)) * 10 * 10) div 100 ,0.02 )">[aligned-ibrp-s-09]-The VAT category tax amount (ibt-117) in a VAT breakdown (ibg-23) where VAT category code (ibt-118) is "Standard rated" MUST equal the VAT category taxable amount (ibt-116) multiplied by the VAT category rate (ibt-119).</assert>
      <assert id="aligned-ibrp-s-10" flag="fatal" test="not(cbc:TaxExemptionReason) and not(cbc:TaxExemptionReasonCode)">[aligned-ibrp-s-10]-A VAT breakdown (ibg-23) with VAT Category code (ibt-118) "Standard rate" MUST not have a VAT exemption reason code (ibt-121) or VAT exemption reason text (ibt-120).</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'N'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="ibr-102-ae" flag="fatal" test="every $rate in xs:decimal(cbc:Percent) satisfies ((exists(//cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID) = &#34;N&#34;][cac:Item/cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]) and  (u:slack(../xs:decimal(cbc:TaxableAmount), sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)=&#34;N&#34;][cac:Item/ cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]/xs:decimal(cbc:LineExtensionAmount)),0.02))) or (exists(//cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID) = &#34;N&#34;][cac:Item/cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate])  and (u:slack(../xs:decimal(cbc:TaxableAmount), sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)=&#34;N&#34;][cac:Item/cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]/xs:decimal(cbc:LineExtensionAmount)) ,0.02))))">[ibr-102-ae]-In a VAT Breakdown (IBG-23) where the VAT category code (IBT-118) is 'Standard rate additional VAT', for each different value of VAT category rate (IBT-119) the VAT category taxable amount (IBT-116) shall equal the sum of Invoice line net amounts (IBT-131) where the VAT category code (IBT-151) is 'Standard rate additional VAT' and the VAT rate (IBT-152) equals the VAT category rate (IBT-119).</assert>
      <assert id="ibr-108-ae" flag="fatal" test="../cbc:TaxAmount = 0">[ibr-108-ae]-In a VAT breakdown (IBG-23), where VAT category code (IBT-118) is 'Standard rate additional VAT', VAT category tax amount (IBT-117) MUST be equal to 0 (zero).</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-e-08" flag="fatal" test="(exists(//cac:InvoiceLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:Amount))))) or (exists(//cac:CreditNoteLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:Amount)))))">[aligned-ibrp-e-08]-In a VAT breakdown (ibg-23) where the VAT category code (ibt-118) is "Exempt from VAT" the VAT category taxable amount (ibt-116) MUST equal the sum of Invoice line net amounts (ibt-131) minus the sum of Document level allowance amounts (ibt-92) plus the sum of Document level charge amounts (ibt-99) where the VAT category codes (ibt-151, ibt-95, ibt-102) are "Exempt from VAT".</assert>
      <assert id="aligned-ibrp-e-09" flag="fatal" test="xs:decimal(../cbc:TaxAmount) = 0">[aligned-ibrp-e-09]-The VAT category tax amount (ibt-117) In a VAT breakdown (ibg-23) where the VAT category code (ibt-118) equals "Exempt from VAT" MUST equal 0 (zero).</assert>
      <assert id="ibr-121-ae" flag="fatal" test="not(cac:TaxCategory)">[ibr-121-ae]-In a VAT breakdown (IBG-23) where VAT category code (IBT-118) is 'Exempt from VAT', VAT category VAT Rate (IBT-119) shall not be provided.</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-e-05" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-e-05]-In an Invoice line (ibg-25) where the Invoiced item VAT category code (ibt-151) is "Exempt from VAT", the Invoiced item VAT rate (ibt-152) MUST be 0 (zero). </assert>
      <assert id="ibr-163-ae" flag="fatal" test="not(../../cac:ItemPriceExtension/cac:TaxAmount/cbc:TaxAmount)">[ibr-163-ae]-In Line VAT information (IBG-30) where Invoiced item VAT category code (IBT-151) is 'Exempt', VAT Line amount [BTAE-08] shall not be there.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-e-06" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-e-06]-In a Document level allowance (ibg-20) where the Document level allowance VAT category code (ibt-95) is "Exempt from VAT", the Document level allowance VAT rate (ibt-96) MUST be 0 (zero).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-e-07" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-e-07]-In a Document level charge (ibg-21) where the Document level charge VAT category code (ibt-102) is "Exempt from VAT", the Document level charge VAT rate (ibt-103) MUST be 0 (zero).</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-z-05" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-z-05]-In an Invoice line (ibg-25) where the Invoiced item VAT category code (ibt-151) is "Zero rated" the Invoiced item VAT rate (ibt-152) MUST be 0 (zero).</assert>
      <assert id="ibr-165-ae" flag="fatal" test="../../cac:ItemPriceExtension/cac:TaxTotal/cbc:TaxAmount = 0">[ibr-165-ae]-In Line VAT information (IBG-30) where Invoiced item VAT category code (IBT-151) is 'Zero Rated', VAT Line amount [BTAE-08] MUST be zero.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-z-06" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-z-06]-In a Document level allowance (ibg-20) where the Document level allowance VAT category code (ibt-95) is "Zero rated" the Document level allowance VAT rate (ibt-96) MUST be 0 (zero).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-z-07" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-z-07]-In a Document level charge (ibg-21) where the Document level charge VAT category code (ibt-102) is "Zero rated" the Document level charge VAT rate (ibt-103) MUST be 0 (zero).</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-z-08" flag="fatal" test="(exists(//cac:InvoiceLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:Amount))))) or (exists(//cac:CreditNoteLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:Amount)))))">[aligned-ibrp-z-08]-In a VAT breakdown (ibg-23) where VAT category code (ibt-118) is "Zero rated" the VAT category taxable amount (ibt-116) MUST equal the sum of Invoice line net amount (ibt-131) minus the sum of Document level allowance amounts (ibt-92) plus the sum of Document level charge amounts (ibt-99) where the VAT category codes (ibt-151, ibt-95, ibt-102) are "Zero rated".</assert>
      <assert id="aligned-ibrp-z-09" flag="fatal" test="xs:decimal(../cbc:TaxAmount) = 0">[aligned-ibrp-z-09]-The VAT category tax amount (ibt-117) in a VAT breakdown (ibg-23) where VAT category code (ibt-118) is "Zero rated" MUST equal 0 (zero).</assert>
      <assert id="ibr-120-ae" flag="fatal" test="../cbc:TaxAmount = 0">[ibr-120-ae]-In a VAT breakdown (IBG-23) where VAT category code (IBT-118) is 'Zero Rated', VAT category VAT Rate (IBT-119) shall equal to 0.</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-o-05" flag="fatal" test="not(cbc:Percent)">[aligned-ibrp-o-05]-An Invoice line (ibg-25) where the VAT category code (ibt-151) is "Not subject to VAT" MUST not contain an Invoiced item VAT rate (ibt-152).</assert>
      <assert id="ibr-164-ae" flag="fatal" test="not(../../cac:ItemPriceExtension/cac:TaxAmount/cbc:TaxAmount)">[ibr-164-ae]-In Line VAT information (IBG-30) where Invoiced item VAT category code (IBT-151) is 'Not Subject to VAT', VAT Line amount [BTAE-08] shall not be there.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-o-06" flag="fatal" test="not(cbc:Percent)">[aligned-ibrp-o-06]-A Document level allowance (ibg-20) where VAT category code (ibt-95) is "Not subject to VAT" MUST not contain a Document level allowance VAT rate (ibt-96).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-o-07" flag="fatal" test="not(cbc:Percent)">[aligned-ibrp-o-07]-A Document level charge (ibg-21) where the VAT category code (ibt-102) is "Not subject to VAT" MUST not contain a Document level charge VAT rate (ibt-103).</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-o-08" flag="fatal" test="(exists(//cac:InvoiceLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:Amount))))) or (exists(//cac:CreditNoteLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:Amount)))))">[aligned-ibrp-o-08]-In a VAT breakdown (ibg-23) where the VAT category code (ibt-118) is " Not subject to VAT" the VAT category taxable amount (ibt-116) MUST equal the sum of Invoice line net amounts (ibt-131) minus the sum of Document level allowance amounts (ibt-92) plus the sum of Document level charge amounts (ibt-99) where the VAT category codes (ibt-151, ibt-95, ibt-102) are "Not subject to VAT".</assert>
      <assert id="aligned-ibrp-o-09" flag="fatal" test="xs:decimal(../cbc:TaxAmount) = 0">[aligned-ibrp-o-09]-The VAT category tax amount (ibt-117) in a VAT breakdown (ibg-23) where the VAT category code (ibt-118) is "Not subject to VAT" MUST be 0 (zero).</assert>
      <assert id="aligned-ibrp-o-11-ae" flag="fatal" test="not(exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']/cbc:Percent))">[aligned-ibrp-o-11-ae]-In a VAT breakdown (IBG-23) where VAT category code (IBT-118) is 'Not Subject to VAT', VAT category tax Rate (IBT-119) shall not be provided.</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'AE'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'AE'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-ae-05-ae" flag="fatal" test="(xs:decimal(cbc:Percent))">[aligned-ibrp-ae-05-ae]-In an Invoice line (ibg-25) where the Invoiced item VAT category code (ibt-151) is "Reverse charge" the Invoiced item VAT rate (ibt-152) MUST be there.</assert>
      <assert id="ibr-103-ae" flag="fatal" test="exists(../../../cac:AccountingCustomerParty/cac:Party/cac:PartyTaxScheme/cbc:CompanyID)">[ibr-103-ae]-When the Invoiced item VAT category code (ibt-151) is VAT reverse charge, then Buyer VAT identifier (ibt-048) MUST be provided.</assert>
      <assert id="ibr-162-ae" flag="fatal" test="../../cac:ItemPriceExtension/cac:TaxTotal/cbc:TaxAmount = 0">[ibr-162-ae]-In Line VAT information (IBG-30) where Invoiced item VAT category code (IBT-151) is 'Reverse charge', VAT Line amount [BTAE-08] MUST be 'zero'.</assert>
      <assert id="ibr-174-ae" flag="fatal" test="not(not(exists(../cac:StandardItemIdentification/cbc:ID)) or ../cac:StandardItemIdentification/cbc:ID/@schemeID != &#34;0160&#34;)">[ibr-174-ae]-In Item Information (IBG-31) where Invoiced VAT category code (IBT-151) is 'Reverse charge',  the corresponding Item Standard Identifier (IBT-157) MUST be there and the Scheme Identifier (IBT-157-1) should  have the code 0160.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='AE'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-ae-06" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-ae-06]-In a Document level allowance (ibg-20) where the Document level allowance VAT category code (ibt-95) is "Reverse charge" the Document level allowance VAT rate (ibt-96) MUST be greater than zero.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='AE'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-ae-07" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-ae-07]-In a Document level charge (ibg-21) where the Document level charge VAT category code (ibt-102) is "Reverse charge" the Document level charge VAT rate (ibt-103) MUST be greater than zero.</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'AE'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='VAT']">
      <assert id="aligned-ibrp-ae-08-ae" flag="fatal" test="(exists(//cac:InvoiceLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='AE']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='AE']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='AE']/xs:decimal(cbc:Amount))))) or (exists(//cac:CreditNoteLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='AE']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='AE']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='AE']/xs:decimal(cbc:Amount)))))">[aligned-ibrp-ae-08-ae]-In a VAT Breakdown (IBG-23) where the VAT category code (IBT-118) is 'Reverse Charge', for each different value of VAT category rate (IBT-119) the VAT category taxable amount (IBT-116) shall equal the sum of Invoice line net amounts (IBT-131) plus the sum of Document level charge amounts (IBT-99) minus the sum of Document level allowance amounts (IBT-92) where the VAT category code (IBT-151, IBT-102, IBT-095) is 'Reverse Charge' and the VAT rate (IBT-152, IBT-103, IBT-096) equals the VAT category rate (IBT-119).</assert>
      <assert id="aligned-ibrp-ae-09-ae" flag="fatal" test="xs:decimal(../cbc:TaxAmount) = 0">[aligned-ibrp-ae-09-ae]-In a VAT breakdown (IBG-23) where the VAT category code (IBT-118) is 'Reverse charge', VAT category tax amount (IBT-117) MUST be equal to 0 (zero).</assert>
    </rule>
    <rule context="cac:Party[cac:PostalAddress/cac:Country/cbc:IdentificationCode = 'AE']/cac:PartyTaxScheme/cbc:CompanyID">
      <assert id="ibr-132-ae" flag="fatal" test="matches(., &#34;^1[a-zA-Z0-9]{12}03$&#34;)">[ibr-132-ae]-VAT identifier [IBT-031, IBT-048, IBT-063, BTAE-14] should be TRN [VAT registration number] and must be 15 alphanumeric digits, starting with 1, ending with 03 .</assert>
    </rule>
    <rule context="cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme/cbc:CompanyID">
      <assert id="ibr-148-ae" flag="fatal" test="matches(., &#34;^1[0-9]{9}$&#34;)">[ibr-148-ae]-The Seller VAT registration identifier (IBT-032) should be TIN (tax identification number) and must be 10 numeric digits and should be of the format 1XXXXXXXXXX.</assert>
    </rule>
    <rule context="cac:TaxScheme/cbc:ID">
      <assert id="ibr-133-ae" flag="fatal" test=". = &#34;VAT&#34; or not(exists(/cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme/cbc:CompanyID))">[ibr-133-ae]-VAT scheme code, if provided in (IBT-095-01) or (IBT-031-1) or (IBT-048-1) or (IBT-063-1) or (IBT-102-1) or (IBT-118-1) shall be 'VAT' except when Seller tax registration identifier (IBT-032) is provided.</assert>
    </rule>
  </pattern>
  <pattern id="Codesmodelaligned">
    <rule flag="fatal" context="cac:DiscrepancyResponse/cbc:ResponseCode">
      <assert id="ibr-001-ae" flag="fatal" test="( ( not(contains(normalize-space(.),' ')) and contains( ' DL8.61.1.A DL8.61.1.B DL8.61.1.C DL8.61.1.D DL8.61.1.E VD ',concat(' ',normalize-space(.),' ') ) ) )">[ibr-001-ae]-Credit note reason code [BTAE-03] value should be from the Reasons for credit note code list.</assert>
    </rule>
    <rule flag="fatal" context="cac:AccountingCustomerParty/cac:Party/cac:PartyLegalEntity/cbc:CompanyID[@schemeAgencyID='PAS']/@schemeAgencyName">
      <assert id="ibr-011-ae" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' 1A AD AE AF AG AI AL AM AO AQ AR AS AT AU AW AX AZ BA BB BD BE BF BG BH BI BJ BL BM BN BO BQ BR BS BT BV BW BY BZ CA CC CD CF CG CH CI CK CL CM CN CO CR CU CV CW CX CY CZ DE DJ DK DM DO DZ EC EE EG EH ER ES ET FI FJ FK FM FO FR GA GB GD GE GF GG GH GI GL GM GN GP GQ GR GS GT GU GW GY HK HM HN HR HT HU ID IE IL IM IN IO IQ IR IS IT JE JM JO JP KE KG KH KI KM KN KP KR KW KY KZ LA LB LC LI LK LR LS LT LU LV LY MA MC MD ME MF MG MH MK ML MM MN MO MP MQ MR MS MT MU MV MW MX MY MZ NA NC NE NF NG NI NL NO NP NR NU NZ OM PA PE PF PG PH PK PL PM PN PR PS PT PW PY QA RE RO RS RU RW SA SB SC SD SE SG SH SI SJ SK SL SM SN SO SR SS ST SV SX SY SZ TC TD TF TG TH TJ TK TL TM TN TO TR TT TV TW TZ UA UG UM US UY UZ VA VC VE VG VI VN VU WF WS XI YE YT ZA ZM ZW ', concat(' ', normalize-space(.), ' '))))">[ibr-011-ae]-Passport issuing country code (BTAE-19) MUST be coded using ISO code list 3166-1.</assert>
    </rule>
    <rule flag="fatal" context="cac:AccountingSupplierParty/cac:Party/cac:PartyLegalEntity/cbc:CompanyID[@schemeAgencyID='PAS']/@schemeAgencyName">
      <assert id="ibr-013-ae" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' 1A AD AE AF AG AI AL AM AO AQ AR AS AT AU AW AX AZ BA BB BD BE BF BG BH BI BJ BL BM BN BO BQ BR BS BT BV BW BY BZ CA CC CD CF CG CH CI CK CL CM CN CO CR CU CV CW CX CY CZ DE DJ DK DM DO DZ EC EE EG EH ER ES ET FI FJ FK FM FO FR GA GB GD GE GF GG GH GI GL GM GN GP GQ GR GS GT GU GW GY HK HM HN HR HT HU ID IE IL IM IN IO IQ IR IS IT JE JM JO JP KE KG KH KI KM KN KP KR KW KY KZ LA LB LC LI LK LR LS LT LU LV LY MA MC MD ME MF MG MH MK ML MM MN MO MP MQ MR MS MT MU MV MW MX MY MZ NA NC NE NF NG NI NL NO NP NR NU NZ OM PA PE PF PG PH PK PL PM PN PR PS PT PW PY QA RE RO RS RU RW SA SB SC SD SE SG SH SI SJ SK SL SM SN SO SR SS ST SV SX SY SZ TC TD TF TG TH TJ TK TL TM TN TO TR TT TV TW TZ UA UG UM US UY UZ VA VC VE VG VI VN VU WF WS XI YE YT ZA ZM ZW ', concat(' ', normalize-space(.), ' '))))">[ibr-013-ae]-Passport issuing country code (BTAE-18) MUST be coded using ISO code list 3166-1.</assert>
    </rule>
    <rule flag="fatal" context="cac:InvoicePeriod/cbc:DescriptionCode">
      <assert id="ibr-005-ae" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' DLY WKY Q15 MTH Q45 Q60 QTR YRL HYR OTH ', concat(' ', normalize-space(.), ' '))))">[ibr-005-ae]-Frequency of billing (BTAE-06) should be taken from the frequency of billing code list.</assert>
    </rule>
    <rule flag="fatal" context="cac:TaxCategory/cbc:ID | cac:ClassifiedTaxCategory/cbc:ID">
      <assert id="ibr-139-ae" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' S E O AE Z N ', concat(' ', normalize-space(.), ' '))))">[ibr-139-ae]-Document level allowance tax category code [IBT-095], Document level charge tax category code [IBT-102], Tax category code [IBT-118], Invoiced item tax category code [IBT-151] should be selected from the aligned tax category code.</assert>
    </rule>
    <rule flag="fatal" context="cbc:NatureCode">
      <assert id="ibr-006-ae" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' DL8.48.8.2 DL8.48.8.1 DL8.48.3.1 DL8.48.3.2 DL8.48.3.3 ', concat(' ', normalize-space(.), ' '))))">[ibr-006-ae]-In Item Information (IBG-31) where Invoiced tax category code (ibt-151) is 'VAT Reverse charge', Type of goods or services (BTAE-09) MUST be selected from the Goods or services subject to RCM Code list.</assert>
    </rule>
  </pattern>
</schema>