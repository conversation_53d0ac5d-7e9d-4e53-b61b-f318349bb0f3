{"globalDefaults": {"xmlElements": {"senderPartyElement": "AccountingSupplierParty", "receiverPartyElement": "AccountingCustomerParty", "endpointIdElement": "cbc:EndpointID", "partyIdElement": "cbc:ID", "customizationIdElement": "cbc:CustomizationID", "profileIdElement": "cbc:ProfileID", "invoiceIdElement": "cbc:ID", "profileExecutionIdElement": "cbc:ProfileExecutionID", "issueDateElement": "cbc:IssueDate", "countryCodeElement": "cbc:IdentificationCode", "countrySubentityElement": "cbc:CountrySubentity"}, "sbdhElements": {"scopeElement": "<PERSON><PERSON>", "typeElement": "Type", "instanceIdentifierElement": "InstanceIdentifier", "countryC1Type": "COUNTRY_C1"}, "sbdhConfiguration": {"headerVersion": "1.0", "authority": "iso6523-actorid-upis", "documentIdScope": {"type": "DOCUMENTID", "identifier": "busdox-docid-qns"}, "processIdScope": {"type": "PROCESSID", "identifier": "cenbii-procid-ubl"}}}, "countries": {"DE": {"name": "Germany", "countryCode": "DE", "as4Config": {"defaultSenderParticipantId": "9915:test-sender-de", "defaultReceiverParticipantId": "9922:test-receiver-de"}, "documentTypes": {"INVOICE": {"classPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2", "typeVersion": "2.1", "documentType": "Invoice"}}}, "CREDITNOTE": {"classPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2", "typeVersion": "2.1", "documentType": "CreditNote"}}}, "APPLICATIONRESPONSE": {"classPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL.sch", "customizationId": "urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2", "typeVersion": "2.1", "documentType": "ApplicationResponse"}}}}}, "FR": {"name": "France", "countryCode": "FR", "as4Config": {"defaultSenderParticipantId": "9915:test-sender-fr", "defaultReceiverParticipantId": "9922:test-receiver-fr"}, "documentTypes": {"INVOICE": {"classPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-FR.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2", "typeVersion": "2.1", "documentType": "Invoice"}}}, "CREDITNOTE": {"classPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-FR.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2", "typeVersion": "2.1", "documentType": "CreditNote"}}}, "APPLICATIONRESPONSE": {"classPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-FR.sch", "customizationId": "urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2", "typeVersion": "2.1", "documentType": "ApplicationResponse"}}}}}, "IT": {"name": "Italy", "countryCode": "IT", "as4Config": {"defaultSenderParticipantId": "9915:test-sender-it", "defaultReceiverParticipantId": "9922:test-receiver-it"}, "documentTypes": {"INVOICE": {"classPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-IT.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2", "typeVersion": "2.1", "documentType": "Invoice"}}}, "CREDITNOTE": {"classPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-IT.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2", "typeVersion": "2.1", "documentType": "CreditNote"}}}, "APPLICATIONRESPONSE": {"classPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-IT.sch", "customizationId": "urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2", "typeVersion": "2.1", "documentType": "ApplicationResponse"}}}}}, "NL": {"name": "Netherlands", "countryCode": "NL", "as4Config": {"defaultSenderParticipantId": "9915:test-sender-nl", "defaultReceiverParticipantId": "9922:test-receiver-nl"}, "documentTypes": {"INVOICE": {"classPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-NL.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2", "typeVersion": "2.1", "documentType": "Invoice"}}}, "CREDITNOTE": {"classPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-NL.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2", "typeVersion": "2.1", "documentType": "CreditNote"}}}, "APPLICATIONRESPONSE": {"classPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-NL.sch", "customizationId": "urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2", "typeVersion": "2.1", "documentType": "ApplicationResponse"}}}}}, "ES": {"name": "Spain", "countryCode": "ES", "as4Config": {"defaultSenderParticipantId": "9915:test-sender-es", "defaultReceiverParticipantId": "9922:test-receiver-es"}, "documentTypes": {"INVOICE": {"classPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-ES.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2", "typeVersion": "2.1", "documentType": "Invoice"}}}, "CREDITNOTE": {"classPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-ES.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2", "typeVersion": "2.1", "documentType": "CreditNote"}}}, "APPLICATIONRESPONSE": {"classPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL-ES.sch", "customizationId": "urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2", "typeVersion": "2.1", "documentType": "ApplicationResponse"}}}}}, "DEFAULT": {"name": "Default/Generic", "countryCode": "BE", "as4Config": {"defaultSenderParticipantId": "9915:test-sender", "defaultReceiverParticipantId": "9922:test-receiver"}, "documentTypes": {"INVOICE": {"classPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2", "typeVersion": "2.1", "documentType": "Invoice"}}}, "CREDITNOTE": {"classPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.creditnote_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL.sch", "customizationId": "urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2", "typeVersion": "2.1", "documentType": "CreditNote"}}}, "APPLICATIONRESPONSE": {"classPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType", "factoryPath": "oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ObjectFactory", "schematronFile": "PEPPOL-EN16931-UBL.sch", "customizationId": "urn:fdc:peppol.eu:2017:poacc:billing:3.0", "profileId": "urn:fdc:peppol.eu:2017:poacc:billing:01:1.0", "as4DocumentTypeId": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1", "xmlParsing": {"defaultValues": {"profileExecutionId": "00000000"}, "sbdhConfiguration": {"standard": "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2", "typeVersion": "2.1", "documentType": "ApplicationResponse"}}}}}}}