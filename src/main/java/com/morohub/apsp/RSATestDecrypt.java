package com.morohub.apsp;

import java.io.FileInputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.OAEPParameterSpec;
import java.security.spec.MGF1ParameterSpec;

public class RSATestDecrypt {
    public static void main(String[] args) throws Exception {
        String keystorePath = "src/main/resources/keystore/cert.p12"; // adjust if needed
        String keystorePassword = "TFd20lJQ4f8j";
        String keyAlias = "cert";
        String keyPassword = "TFd20lJQ4f8j";
        String encryptedKeyBase64 =
            "mFo46zYDQrbhRyy0nTHrt9JAjEgxXvHhfYVPcRMXCOqiFdwShQa6F/E5lbvRDz6lTuqweLM7UMTm3h1dywEvNDXKterWUiu6AnnAJyYj2uGGDhbnOU+hPfREhPcI58JTuuc41vuY2UfKSlV8UBetDtazbKXJsixFaIUV+U4s0vyYfS1hcjYEQQNbSgE50jRZp81VjGJi9F666jn5+u0JVium231YxfIW08OeHZGIenUGySyG61vGonvDxNvIKkgjxjHW9Qp+mpwrYogIYYoq5YFviJ/VfdWDVI9tVmWMlu3EQDbtl7a23WglE0UNL3wmtNAOT473G6BDG86+X+YQow==";

        // Remove whitespace
        encryptedKeyBase64 = encryptedKeyBase64.replaceAll("\\s+", "");
        byte[] encryptedKeyBytes = Base64.getDecoder().decode(encryptedKeyBase64);

        System.out.println("Encrypted data length: " + encryptedKeyBytes.length + " bytes");

        KeyStore ks = KeyStore.getInstance("PKCS12");
        try (FileInputStream fis = new FileInputStream(keystorePath)) {
            ks.load(fis, keystorePassword.toCharArray());
        }
        PrivateKey privateKey = (PrivateKey) ks.getKey(keyAlias, keyPassword.toCharArray());

        if (privateKey == null) {
            System.out.println("❌ Private key not found for alias: " + keyAlias);
            return;
        }

        System.out.println("✅ Private key loaded successfully");
        System.out.println("Private key algorithm: " + privateKey.getAlgorithm());
        System.out.println("Private key format: " + privateKey.getFormat());

        // Try different RSA padding schemes commonly used in the codebase
        String[] paddingSchemes = {
            "RSA/ECB/PKCS1Padding",
            "RSA/ECB/OAEPPadding",
            "RSA/ECB/OAEPWithSHA-1AndMGF1Padding",
            "RSA/ECB/OAEPWithSHA-256AndMGF1Padding",
            "RSA"  // Default RSA transformation
        };

        boolean decryptionSuccessful = false;

        for (String padding : paddingSchemes) {
            try {
                System.out.println("\n🔐 Trying padding scheme: " + padding);

                Cipher rsaCipher = Cipher.getInstance(padding);

                // For OAEP with SHA-256, set explicit parameters as done in AS4CryptoConfiguration
                if ("RSA/ECB/OAEPWithSHA-256AndMGF1Padding".equals(padding)) {
                    OAEPParameterSpec oaepSpec = new OAEPParameterSpec(
                        "SHA-256", "MGF1",
                        new MGF1ParameterSpec("SHA-256"),
                        javax.crypto.spec.PSource.PSpecified.DEFAULT
                    );
                    rsaCipher.init(Cipher.DECRYPT_MODE, privateKey, oaepSpec);
                } else {
                    rsaCipher.init(Cipher.DECRYPT_MODE, privateKey);
                }

                byte[] symmetricKey = rsaCipher.doFinal(encryptedKeyBytes);

                System.out.println("✅ Decryption successful with padding: " + padding);
                System.out.println("Symmetric key length: " + symmetricKey.length + " bytes");

                // Print the decrypted key in hex format for verification
                StringBuilder hexKey = new StringBuilder();
                for (byte b : symmetricKey) {
                    hexKey.append(String.format("%02X ", b));
                }
                System.out.println("Symmetric key (hex): " + hexKey.toString().trim());

                // Try to interpret as string (if it's a text-based key)
                try {
                    String keyAsString = new String(symmetricKey, "UTF-8");
                    if (keyAsString.matches("^[\\x20-\\x7E]*$")) { // Printable ASCII
                        System.out.println("Symmetric key (string): " + keyAsString);
                    }
                } catch (Exception e) {
                    // Ignore if not valid UTF-8
                }

                decryptionSuccessful = true;
                break;

            } catch (javax.crypto.BadPaddingException e) {
                System.out.println("❌ Padding error with " + padding + ": " + e.getMessage());
            } catch (Exception e) {
                System.out.println("❌ Decryption failed with " + padding + ": " + e.getMessage());
            }
        }

        if (!decryptionSuccessful) {
            System.out.println("\n❌ All decryption attempts failed. Possible causes:");
            System.out.println("   - Wrong private key");
            System.out.println("   - Data was encrypted with a different public key");
            System.out.println("   - Data is corrupted");
            System.out.println("   - Different encryption algorithm was used");
        }
    }
}