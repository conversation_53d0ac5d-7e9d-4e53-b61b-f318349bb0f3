package com.morohub.apsp.core.service;

import com.morohub.apsp.common.dto.RecipientEndpointResponseDTO;
import com.morohub.apsp.core.entity.RecipientEndpoint;
import com.morohub.apsp.core.repository.RecipientEndpointRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing recipient endpoints and URLs from database
 */
@Service
public class RecipientService {

    private static final Logger logger = LoggerFactory.getLogger(RecipientService.class);

    @Autowired
    private RecipientEndpointRepository recipientEndpointRepository;

    /**
     * Get recipient endpoint information by endpoint ID
     */
    public RecipientEndpointResponseDTO getRecipientEndpoint(String endpointId) {
        try {
            logger.debug("Looking up recipient endpoint for ID: {}", endpointId);
            
            Optional<RecipientEndpoint> endpointOpt = recipientEndpointRepository.findByEndpointId(endpointId);
            
            if (endpointOpt.isPresent()) {
                RecipientEndpoint endpoint = endpointOpt.get();
                logger.debug("Found recipient endpoint: {} -> {}", endpointId, endpoint.getEndpointUrl());
                
                return convertToDTO(endpoint);
            } else {
                // Return default endpoint if not found
                logger.warn("Recipient endpoint not found for ID: {}, using default", endpointId);
                return getDefaultEndpoint(endpointId);
            }
            
        } catch (Exception e) {
            logger.error("Error retrieving recipient endpoint for ID: " + endpointId, e);
            return getDefaultEndpoint(endpointId);
        }
    }

    /**
     * Get recipient endpoint by participant ID
     */
    public RecipientEndpointResponseDTO getRecipientByParticipantId(String participantId) {
        try {
            logger.debug("Looking up recipient by participant ID: {}", participantId);
            
            Optional<RecipientEndpoint> endpointOpt = recipientEndpointRepository.findByParticipantId(participantId);
            
            if (endpointOpt.isPresent()) {
                RecipientEndpoint endpoint = endpointOpt.get();
                logger.debug("Found recipient by participant ID: {} -> {}", participantId, endpoint.getEndpointUrl());
                
                return convertToDTO(endpoint);
            } else {
                logger.warn("Recipient not found for participant ID: {}", participantId);
                throw new RuntimeException("Recipient not found for participant ID: " + participantId);
            }
            
        } catch (Exception e) {
            logger.error("Error retrieving recipient by participant ID: " + participantId, e);
            throw new RuntimeException("Failed to retrieve recipient: " + e.getMessage(), e);
        }
    }

    /**
     * Get all active recipient endpoints
     */
    public List<RecipientEndpointResponseDTO> getAllActiveRecipients() {
        try {
            logger.debug("Retrieving all active recipient endpoints");
            
            List<RecipientEndpoint> endpoints = recipientEndpointRepository.findByActiveTrue();
            
            return endpoints.stream()
                    .map(this::convertToDTO)
                    .toList();
                    
        } catch (Exception e) {
            logger.error("Error retrieving active recipients", e);
            throw new RuntimeException("Failed to retrieve active recipients: " + e.getMessage(), e);
        }
    }

    /**
     * Save or update recipient endpoint
     */
    public RecipientEndpointResponseDTO saveRecipientEndpoint(String endpointId, String participantId, String endpointUrl) {
        try {
            logger.info("Saving recipient endpoint: {} -> {}", endpointId, endpointUrl);
            
            RecipientEndpoint endpoint = recipientEndpointRepository.findByEndpointId(endpointId)
                    .orElse(new RecipientEndpoint());
            
            endpoint.setEndpointId(endpointId);
            endpoint.setParticipantId(participantId);
            endpoint.setEndpointUrl(endpointUrl);
            endpoint.setActive(true);
            endpoint.setLastUpdated(java.time.LocalDateTime.now());
            
            RecipientEndpoint saved = recipientEndpointRepository.save(endpoint);
            
            logger.info("Successfully saved recipient endpoint: {}", saved.getEndpointId());
            return convertToDTO(saved);
            
        } catch (Exception e) {
            logger.error("Error saving recipient endpoint", e);
            throw new RuntimeException("Failed to save recipient endpoint: " + e.getMessage(), e);
        }
    }

    /**
     * Convert entity to DTO
     */
    private RecipientEndpointResponseDTO convertToDTO(RecipientEndpoint endpoint) {
        RecipientEndpointResponseDTO dto = new RecipientEndpointResponseDTO();
        dto.setParticipantId(endpoint.getParticipantId());
        dto.setEndpointUrl(endpoint.getEndpointUrl());
        dto.setCertificateInfo(endpoint.getCertificateInfo());
        dto.setTransportProfile(endpoint.getTransportProfile());
        dto.setActive(endpoint.isActive());
        dto.setLastUpdated(endpoint.getLastUpdated() != null ? endpoint.getLastUpdated().toString() : null);
        
        return dto;
    }

    /**
     * Get default endpoint when none is found in database
     */
    private RecipientEndpointResponseDTO getDefaultEndpoint(String endpointId) {
        logger.info("Using default endpoint for: {}", endpointId);
        
        RecipientEndpointResponseDTO defaultEndpoint = new RecipientEndpointResponseDTO();
        defaultEndpoint.setParticipantId("9908:default");
        defaultEndpoint.setEndpointUrl("https://httpbin.org/post"); // Default test endpoint
        defaultEndpoint.setActive(true);
        defaultEndpoint.setTransportProfile("peppol-transport-as4-v2_0");
        
        return defaultEndpoint;
    }
}
