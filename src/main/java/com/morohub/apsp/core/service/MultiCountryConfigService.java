package com.morohub.apsp.core.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.morohub.apsp.config.model.CountryConfig;
import com.morohub.apsp.config.model.DocumentTypeConfig;
import com.morohub.apsp.config.model.MultiCountryConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * Service for managing multi-country UBL document type configurations
 * Loads configuration from JSON file and provides dynamic class/schematron selection
 */
@Service
public class MultiCountryConfigService {

    private static final Logger logger = LoggerFactory.getLogger(MultiCountryConfigService.class);

    @Autowired
    private ObjectMapper objectMapper;

    private MultiCountryConfig config;

    @PostConstruct
    public void loadConfiguration() {
        try {
            logger.info("Loading multi-country configuration...");
            
            ClassPathResource resource = new ClassPathResource("country-config.json");
            if (!resource.exists()) {
                throw new RuntimeException("Configuration file 'country-config.json' not found in classpath");
            }

            try (InputStream inputStream = resource.getInputStream()) {
                config = objectMapper.readValue(inputStream, MultiCountryConfig.class);
                logger.info("✅ Multi-country configuration loaded successfully");
                logConfigurationSummary();
            }

        } catch (IOException e) {
            logger.error("❌ Failed to load multi-country configuration", e);
            throw new RuntimeException("Failed to load country configuration", e);
        }
    }

    /**
     * Get document type configuration for a specific country and document type
     */
    public DocumentTypeConfig getDocumentTypeConfig(String countryCode, String documentType) {
        if (config == null) {
            throw new IllegalStateException("Configuration not loaded");
        }

        DocumentTypeConfig docConfig = config.getDocumentTypeConfig(countryCode, documentType);
        
        if (docConfig == null) {
            logger.warn("No configuration found for country: {} and document type: {}, using DEFAULT", 
                countryCode, documentType);
            docConfig = config.getDocumentTypeConfig("DEFAULT", documentType);
        }

        if (docConfig == null) {
            throw new IllegalArgumentException(
                String.format("No configuration found for document type: %s (country: %s)", 
                    documentType, countryCode));
        }

        logger.debug("Using configuration for {}/{}: {}", countryCode, documentType, docConfig);
        return docConfig;
    }

    /**
     * Get UBL class for a specific country and document type
     */
    public Class<?> getUBLClass(String countryCode, String documentType) throws ClassNotFoundException {
        DocumentTypeConfig config = getDocumentTypeConfig(countryCode, documentType);
        return Class.forName(config.getClassPath());
    }

    /**
     * Get factory class for a specific country and document type
     */
    public Class<?> getFactoryClass(String countryCode, String documentType) throws ClassNotFoundException {
        DocumentTypeConfig config = getDocumentTypeConfig(countryCode, documentType);
        return Class.forName(config.getFactoryPath());
    }

    /**
     * Get schematron file for a specific country and document type (backward compatibility)
     */
    public String getSchematronFile(String countryCode, String documentType) {
        DocumentTypeConfig config = getDocumentTypeConfig(countryCode, documentType);
        return config.getSchematronFile();
    }

    /**
     * Get schematron files for a specific country and document type
     */
    public List<String> getSchematronFiles(String countryCode, String documentType) {
        DocumentTypeConfig config = getDocumentTypeConfig(countryCode, documentType);
        return config.getSchematronFiles();
    }

    /**
     * Get customization ID for a specific country and document type
     */
    public String getCustomizationId(String countryCode, String documentType) {
        DocumentTypeConfig config = getDocumentTypeConfig(countryCode, documentType);
        return config.getCustomizationId();
    }

    /**
     * Get profile ID for a specific country and document type
     */
    public String getProfileId(String countryCode, String documentType) {
        DocumentTypeConfig config = getDocumentTypeConfig(countryCode, documentType);
        return config.getProfileId();
    }

    /**
     * Check if country and document type combination is supported
     */
    public boolean isSupported(String countryCode, String documentType) {
        if (config == null) {
            return false;
        }
        return config.supportsDocumentType(countryCode, documentType) || 
               config.supportsDocumentType("DEFAULT", documentType);
    }

    /**
     * Get all supported countries
     */
    public java.util.Set<String> getSupportedCountries() {
        if (config == null || config.getCountries() == null) {
            return java.util.Collections.emptySet();
        }
        return config.getCountries().keySet();
    }

    /**
     * Get all supported document types for a country
     */
    public java.util.Set<String> getSupportedDocumentTypes(String countryCode) {
        if (config == null) {
            return java.util.Collections.emptySet();
        }
        
        CountryConfig countryConfig = config.getCountry(countryCode);
        if (countryConfig == null || countryConfig.getDocumentTypes() == null) {
            return java.util.Collections.emptySet();
        }
        
        return countryConfig.getDocumentTypes().keySet();
    }

    /**
     * Log configuration summary for debugging
     */
    private void logConfigurationSummary() {
        if (config == null || config.getCountries() == null) {
            logger.warn("No countries configured");
            return;
        }

        logger.info("📋 Configuration Summary:");
        config.getCountries().forEach((countryCode, countryConfig) -> {
            logger.info("  🌍 {} ({}): {} document types", 
                countryCode, 
                countryConfig.getName(),
                countryConfig.getDocumentTypes() != null ? countryConfig.getDocumentTypes().size() : 0);
            
            if (countryConfig.getDocumentTypes() != null) {
                countryConfig.getDocumentTypes().forEach((docType, docConfig) -> {
                    logger.debug("    📄 {}: {} | {}", 
                        docType, 
                        docConfig.getClassPath(), 
                        docConfig.getSchematronFile());
                });
            }
        });
    }

    /**
     * Reload configuration (useful for testing or runtime updates)
     */
    public void reloadConfiguration() {
        logger.info("Reloading multi-country configuration...");
        loadConfiguration();
    }

    /**
     * Get the full configuration (for debugging/admin purposes)
     */
    public MultiCountryConfig getFullConfiguration() {
        return config;
    }
}
