package com.morohub.apsp.core.repository;

import com.morohub.apsp.core.entity.RecipientEndpoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing recipient endpoint data
 */
@Repository
public interface RecipientEndpointRepository extends JpaRepository<RecipientEndpoint, Long> {

    /**
     * Find recipient endpoint by endpoint ID
     */
    Optional<RecipientEndpoint> findByEndpointId(String endpointId);

    /**
     * Find recipient endpoint by participant ID
     */
    Optional<RecipientEndpoint> findByParticipantId(String participantId);

    /**
     * Find all active recipient endpoints
     */
    List<RecipientEndpoint> findByActiveTrue();

    /**
     * Find all inactive recipient endpoints
     */
    List<RecipientEndpoint> findByActiveFalse();

    /**
     * Find recipient endpoints by transport profile
     */
    List<RecipientEndpoint> findByTransportProfile(String transportProfile);

    /**
     * Find recipient endpoints containing URL pattern
     */
    @Query("SELECT r FROM RecipientEndpoint r WHERE r.endpointUrl LIKE %:urlPattern% AND r.active = true")
    List<RecipientEndpoint> findByEndpointUrlContaining(@Param("urlPattern") String urlPattern);

    /**
     * Check if endpoint ID exists
     */
    boolean existsByEndpointId(String endpointId);

    /**
     * Check if participant ID exists
     */
    boolean existsByParticipantId(String participantId);

    /**
     * Count active endpoints
     */
    @Query("SELECT COUNT(r) FROM RecipientEndpoint r WHERE r.active = true")
    long countActiveEndpoints();

    /**
     * Find endpoints by endpoint ID pattern (for wildcard searches)
     */
    @Query("SELECT r FROM RecipientEndpoint r WHERE r.endpointId LIKE %:pattern% AND r.active = true")
    List<RecipientEndpoint> findByEndpointIdPattern(@Param("pattern") String pattern);
}
