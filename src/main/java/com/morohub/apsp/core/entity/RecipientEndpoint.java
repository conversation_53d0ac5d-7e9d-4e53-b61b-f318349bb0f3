package com.morohub.apsp.core.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * Entity representing recipient endpoint information stored in database
 */
@Entity
@Table(name = "recipient_endpoints")
public class RecipientEndpoint {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "endpoint_id", unique = true, nullable = false)
    private String endpointId;

    @Column(name = "participant_id", nullable = false)
    private String participantId;

    @Column(name = "endpoint_url", nullable = false)
    private String endpointUrl;

    @Column(name = "certificate_info")
    private String certificateInfo;

    @Column(name = "transport_profile")
    private String transportProfile = "peppol-transport-as4-v2_0";

    @Column(name = "active")
    private boolean active = true;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "last_updated")
    private LocalDateTime lastUpdated;

    @Column(name = "description")
    private String description;

    // Constructors
    public RecipientEndpoint() {
        this.createdDate = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
    }

    public RecipientEndpoint(String endpointId, String participantId, String endpointUrl) {
        this();
        this.endpointId = endpointId;
        this.participantId = participantId;
        this.endpointUrl = endpointUrl;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEndpointId() {
        return endpointId;
    }

    public void setEndpointId(String endpointId) {
        this.endpointId = endpointId;
    }

    public String getParticipantId() {
        return participantId;
    }

    public void setParticipantId(String participantId) {
        this.participantId = participantId;
    }

    public String getEndpointUrl() {
        return endpointUrl;
    }

    public void setEndpointUrl(String endpointUrl) {
        this.endpointUrl = endpointUrl;
    }

    public String getCertificateInfo() {
        return certificateInfo;
    }

    public void setCertificateInfo(String certificateInfo) {
        this.certificateInfo = certificateInfo;
    }

    public String getTransportProfile() {
        return transportProfile;
    }

    public void setTransportProfile(String transportProfile) {
        this.transportProfile = transportProfile;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @PreUpdate
    public void preUpdate() {
        this.lastUpdated = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "RecipientEndpoint{" +
                "id=" + id +
                ", endpointId='" + endpointId + '\'' +
                ", participantId='" + participantId + '\'' +
                ", endpointUrl='" + endpointUrl + '\'' +
                ", active=" + active +
                '}';
    }
}
