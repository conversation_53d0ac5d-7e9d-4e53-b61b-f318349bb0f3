2025-06-26 12:41:04.422 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 4240 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-26 12:41:04.425 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-26 12:41:04.426 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-26 12:41:07.821 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-26 12:41:07.839 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-26 12:41:12.753 [main] INFO  c.m.a.c.s.MultiCountryConfigService - Loading multi-country configuration...
2025-06-26 12:41:12.922 [main] INFO  c.m.a.c.s.MultiCountryConfigService - ✅ Multi-country configuration loaded successfully
2025-06-26 12:41:12.923 [main] INFO  c.m.a.c.s.MultiCountryConfigService - 📋 Configuration Summary:
2025-06-26 12:41:12.924 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DE (Germany): 3 document types
2025-06-26 12:41:12.927 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:41:12.927 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:41:12.927 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:41:12.927 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 FR (France): 3 document types
2025-06-26 12:41:12.929 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 12:41:12.929 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 12:41:12.930 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 12:41:12.931 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 IT (Italy): 3 document types
2025-06-26 12:41:12.931 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 12:41:12.932 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 12:41:12.932 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 12:41:12.933 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 NL (Netherlands): 3 document types
2025-06-26 12:41:12.933 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 12:41:12.934 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 12:41:12.934 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 12:41:12.934 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 ES (Spain): 3 document types
2025-06-26 12:41:12.934 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 12:41:12.935 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 12:41:12.935 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 12:41:12.935 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-26 12:41:12.936 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:41:12.936 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:41:12.940 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:41:12.967 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Country configuration loaded successfully
2025-06-26 12:41:12.968 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-26 12:41:13.310 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-26 12:41:13.398 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-26 12:41:13.409 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-26 12:41:13.435 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-26 12:41:13.436 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-26 12:41:13.436 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-26 12:41:13.437 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-26 12:41:13.440 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-26 12:41:13.440 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-26 12:41:13.441 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-26 12:41:13.441 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-26 12:41:13.451 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-26 12:41:13.451 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-26 12:41:13.452 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-26 12:41:13.452 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-26 12:41:13.452 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 12:41:13.452 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-26 12:41:13.452 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-26 12:41:13.472 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-26 12:41:13.474 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-26 12:41:13.474 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-26 12:41:13.546 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-26 12:41:13.547 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-26 12:41:13.581 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-26 12:41:13.588 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-26 12:41:13.967 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-26 12:41:13.968 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-26 12:41:14.246 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-26 12:41:15.190 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-26 12:41:15.191 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-26 12:41:15.206 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 11.988 seconds (process running for 13.342)
2025-06-26 12:41:24.187 [http-nio-8080-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PEPPOL-EN16931-UBL.sch
2025-06-26 12:41:25.006 [http-nio-8080-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-26 12:41:25.007 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - === Starting AS4 Conversion ===
2025-06-26 12:41:25.007 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - Transaction ID: 85f0ae17-86eb-4449-9fd5-b429f3c87c98
2025-06-26 12:41:25.007 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - Mode: dummy, Security: true
2025-06-26 12:41:25.007 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - Extracting AS4 metadata from UBL XML
2025-06-26 12:41:25.037 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Found country code in address: AE
2025-06-26 12:41:25.037 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 📄 Document type: INVOICE, Country: AE
2025-06-26 12:41:25.037 [http-nio-8080-exec-3] WARN  c.m.a.c.CountryConfigurationService - ⚠️ Country AE not found, using DEFAULT configuration
2025-06-26 12:41:25.038 [http-nio-8080-exec-3] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingSupplierParty: **********
2025-06-26 12:41:25.039 [http-nio-8080-exec-3] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingSupplierParty: 0235
2025-06-26 12:41:25.080 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 👤 Extracted sender participant ID: ********** with scheme: 0235
2025-06-26 12:41:25.081 [http-nio-8080-exec-3] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingCustomerParty: **********
2025-06-26 12:41:25.081 [http-nio-8080-exec-3] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingCustomerParty: 0235
2025-06-26 12:41:25.081 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 👥 Extracted receiver participant ID: ********** with scheme: 0235
2025-06-26 12:41:25.086 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 📋 Using document type ID: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1
2025-06-26 12:41:25.088 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 📋 Extracted ProfileExecutionID: ********
2025-06-26 12:41:25.089 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 📅 Extracted IssueDate: 2025-02-06
2025-06-26 12:41:25.090 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 metadata extracted successfully
2025-06-26 12:41:25.090 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 📄 Invoice ID: AE-01TEST
2025-06-26 12:41:25.090 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 📊 Message ID: MSG-1750921885089
2025-06-26 12:41:25.090 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 👤 Sender: [PeppolParticipantIdentifier@0x76164004: scheme=iso6523-actorid-upis; value=**********]
2025-06-26 12:41:25.092 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 👥 Receiver: [PeppolParticipantIdentifier@0x0bfc963f: scheme=iso6523-actorid-upis; value=**********]
2025-06-26 12:41:25.092 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 📋 Document Type: [PeppolDocumentTypeIdentifier@0x3dd176ab: scheme=busdox-docid-qns; value=urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1]
2025-06-26 12:41:25.092 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - ⚙️ Process: [PeppolProcessIdentifier@0x734913ab: scheme=cenbii-procid-ubl; value=urn:peppol:bis:billing]
2025-06-26 12:41:25.092 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Country Code: BE
2025-06-26 12:41:25.092 [http-nio-8080-exec-3] DEBUG c.m.a.c.service.AS4ConversionService - Validating AS4 message metadata...
2025-06-26 12:41:25.093 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 message validation passed
2025-06-26 12:41:25.093 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION/TEST MODE: Using Phase4PeppolSender
2025-06-26 12:41:25.093 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🏭 Sending AS4 message in dummy mode
2025-06-26 12:41:25.093 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Setting country code for SBDH: BE
2025-06-26 12:41:25.093 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 👤 From Party ID: **********
2025-06-26 12:41:25.093 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 👥 To Party ID: **********
2025-06-26 12:41:25.093 [http-nio-8080-exec-3] WARN  c.m.a.c.CountryConfigurationService - ⚠️ Country BE not found, using DEFAULT configuration
2025-06-26 12:41:25.173 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - ✅ Custom SBDH created with:
2025-06-26 12:41:25.174 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService -    📋 Instance ID: ********-AE-01TEST
2025-06-26 12:41:25.174 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService -    📅 Creation Date: 2025-02-06T12:41:25.095042900
2025-06-26 12:41:25.174 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService -    👤 Sender: 0235:**********
2025-06-26 12:41:25.174 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService -    👥 Receiver: 0235:**********
2025-06-26 12:41:25.174 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService -    🌍 Country: BE
2025-06-26 12:41:25.540 [http-nio-8080-exec-3] DEBUG c.m.a.config.AS4CryptoConfiguration - Configuring Phase4PeppolSender with crypto settings for mode: dummy
2025-06-26 12:41:25.541 [http-nio-8080-exec-3] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Using configured crypto factory for signing
2025-06-26 12:41:25.541 [http-nio-8080-exec-3] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Crypto factory will use key alias: cert
2025-06-26 12:41:25.541 [http-nio-8080-exec-3] DEBUG c.m.a.config.AS4CryptoConfiguration - 🧪 DUMMY MODE: Configuring sender for testing with relaxed validation
2025-06-26 12:41:25.542 [http-nio-8080-exec-3] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Phase4PeppolSender configured with crypto settings
2025-06-26 12:41:25.542 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Crypto factory configured: ✅ Available
2025-06-26 12:41:25.545 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-26 12:41:25.546 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION MODE: Using real Peppol network
2025-06-26 12:41:25.546 [http-nio-8080-exec-3] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE with direct endpoint - FOR TESTING ONLY
2025-06-26 12:41:25.546 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Using direct endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 12:41:25.546 [http-nio-8080-exec-3] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE: Using direct endpoint with certificate validation BYPASSED
2025-06-26 12:41:25.547 [http-nio-8080-exec-3] WARN  c.m.a.c.s.TrustAllEndpointDetailProvider - ⚠️ TrustAllEndpointDetailProvider created - Certificate validation BYPASSED for endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 12:41:25.548 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-26 12:41:25.548 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - ✅ Certificate validation BYPASSED for development/testing
2025-06-26 12:41:25.551 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Initializing TrustAllEndpointDetailProvider for participant: [PeppolParticipantIdentifier@0x0bfc963f: scheme=iso6523-actorid-upis; value=**********]
2025-06-26 12:41:25.552 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate for receiver AP to bypass validation
2025-06-26 12:41:25.552 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate from keystore for receiver AP
2025-06-26 12:41:25.555 [http-nio-8080-exec-3] INFO  c.m.a.c.s.TrustAllEndpointDetailProvider - ✅ Dummy certificate loaded for receiver AP - using same cert as sender
2025-06-26 12:41:25.555 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-26 12:41:25.555 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-26 12:41:26.866 [http-nio-8080-exec-3] WARN  c.h.p.u.CertificateRevocationChecker - OCSP/CRL revocation check took 1231 milliseconds which is too long
2025-06-26 12:41:26.871 [http-nio-8080-exec-3] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Getting receiver AP endpoint URL: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 12:41:27.807 [http-nio-8080-exec-3] WARN  o.a.j.x.d.internal.dom.DOMReference - The input bytes to the digest operation are null. This may be due to a problem with the Reference URI or its Transforms.
2025-06-26 12:41:28.315 [http-nio-8080-exec-1] INFO  c.m.a.a.c.ReverseFlowController - 🔄 AS4 message endpoint called - redirecting to Phase4 servlet
2025-06-26 12:41:28.348 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - === AS4 Servlet: Processing incoming AS4 message ===
2025-06-26 12:41:28.349 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📨 Request URI: /reverse-flow/as4/
2025-06-26 12:41:28.349 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📋 Content-Type: multipart/related;    boundary="----=_Part_0_554630611.1750921887943";    type="application/soap+xml"; charset=UTF-8
2025-06-26 12:41:28.349 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📏 Content-Length: 15138
2025-06-26 12:41:28.349 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Method: POST
2025-06-26 12:41:28.350 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📦 Received AS4 message, length: 14979
2025-06-26 12:41:28.351 [http-nio-8080-exec-4] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔍 Extracting MessageId from AS4 message
2025-06-26 12:41:28.351 [http-nio-8080-exec-4] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ Found MessageId: 27360108-58e8-4e02-9417-1be2b554fd9b@phase4
2025-06-26 12:41:48.230 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - === Processing AS4 message content ===
2025-06-26 12:41:48.230 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📦 AS4 message length: 14979
2025-06-26 12:41:48.231 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 AS4 message preview: 
------=_Part_0_554630611.1750921887943
Content-Type: application/soap+xml;charset=UTF-8
Content-Transfer-Encoding: binary

<?xml version="1.0" encoding="UTF-8"?><S12:Envelope xmlns:S12="http://www.w3.org/2003/05/soap-envelope"><S12:Header><wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" S12:mustUnderstand="true"><wsse:BinarySecurityToke
2025-06-26 12:42:06.693 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Original UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>********-AE-01TEST</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-02-06T12:41:25.095042900</sh:CreationDateAndTime>
      </sh:DocumentIdentification>
  
2025-06-26 12:42:06.694 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Checking if UBL XML contains SBDH headers
2025-06-26 12:42:06.699 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📋 SBDH headers detected, extracting invoice document
2025-06-26 12:42:06.699 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Root element namespace: http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader
2025-06-26 12:42:06.701 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Found 1 Invoice nodes
2025-06-26 12:42:06.701 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Invoice node namespace: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2, local name: Invoice
2025-06-26 12:42:06.734 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔧 Converted node to XML string, length: 11967
2025-06-26 12:42:06.734 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-26 12:42:06.734 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted Invoice document from SBDH, length: 11967
2025-06-26 12:42:06.734 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted clean UBL document, length: 11967
2025-06-26 12:42:06.734 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Clean UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-26 12:42:06.734 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting country code from UBL XML
2025-06-26 12:42:06.739 [http-nio-8080-exec-4] WARN  c.m.a.c.s.Phase4AS4ReceiverService - ⚠️ No country code found in UBL XML, using DEFAULT
2025-06-26 12:42:06.740 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting document type from UBL XML
2025-06-26 12:42:06.743 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Detected document type: INVOICE from root element: Invoice
2025-06-26 12:42:06.744 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 🌍 Detected country: DEFAULT, document type: INVOICE
2025-06-26 12:42:06.744 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - Processing incoming UBL XML for country: DEFAULT and document type: INVOICE
2025-06-26 12:42:06.744 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - Parsing UBL XML to object using country-specific configuration: DEFAULT/INVOICE
2025-06-26 12:42:06.744 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.MultiCountryConfigService - Using configuration for DEFAULT/INVOICE: DocumentTypeConfig{classPath='oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType', factoryPath='oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory', schematronFile='PEPPOL-EN16931-UBL.sch', customizationId='urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0', profileId='urn:fdc:peppol.eu:2017:poacc:billing:01:1.0'}
2025-06-26 12:42:06.751 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 🔍 Using UBL class for parsing: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-06-26 12:42:06.751 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 UBL XML to parse (first 1000 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-26 12:42:06.752 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 Root element: <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
2025-06-26 12:42:06.752 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - ✅ Found expected root element: Invoice
2025-06-26 12:42:06.752 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating JAXB context for class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
2025-06-26 12:42:06.753 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-26 12:42:06.755 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-26 12:42:06.792 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-06-26 12:42:06.862 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-06-26 12:42:06.863 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-06-26 12:42:06.864 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-26 12:42:06.864 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-26 12:42:07.912 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 XML to unmarshal (first 500 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-26 12:42:07.941 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Successfully unmarshalled XML to object of type: jakarta.xml.bind.JAXBElement
2025-06-26 12:42:07.941 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 🔄 Handling response for DEFAULT/INVOICE with file writing as default
2025-06-26 12:42:07.947 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 📄 Response written to file: C:\Users\<USER>\backy\as4-main\responses\response_DEFAULT_INVOICE_20250626_124207_941.xml
2025-06-26 12:42:07.947 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 📊 File size: 11967 bytes
2025-06-26 12:42:07.947 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - Extracting sender ID from UBL document: JAXBElement
2025-06-26 12:42:07.948 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - No sender ID found in UBL document
2025-06-26 12:42:07.948 [http-nio-8080-exec-4] WARN  c.m.apsp.core.service.InvoiceService - ⚠️ No sender ID found in UBL document, cannot send response
2025-06-26 12:42:07.948 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - ✅ Response successfully written to file and sent via REST
2025-06-26 12:42:07.948 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - ✅ AS4 message processed successfully
2025-06-26 12:42:07.948 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 message processed successfully: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDate>
   <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
   <cbc:Note>Tax invoice</cbc:Note>
   <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
   <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
   <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
   <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
   <cac:InvoicePeriod>
      <cbc:StartDate>2025-01-31</cbc:StartDate>
      <cbc:EndDate>2025-02-06</cbc:EndDate>
   </cac:InvoicePeriod>
   <cac:OrderReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
   </cac:OrderReference>
   <cac:BillingReference>
      <cac:InvoiceDocumentReference>
         <cbc:ID>INV-234-2025</cbc:ID>
         <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      </cac:InvoiceDocumentReference>
   </cac:BillingReference>
   <cac:DespatchDocumentReference>
      <cbc:ID>Memo-1000</cbc:ID>
   </cac:DespatchDocumentReference>
   <cac:OriginatorDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
   </cac:OriginatorDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cac:ExternalReference>
            <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
         </cac:ExternalReference>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:ProjectReference>
      <cbc:ID>Regular work</cbc:ID>
   </cac:ProjectReference>
   <cac:AccountingSupplierParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Party Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Sharjah</cbc:CityName>
            <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********02003</cbc:CompanyID>
            <!--  IBT-031 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-030, BTAE-15, BTAE-12 -->
            <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingSupplierParty>
   <cac:AccountingCustomerParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Buyer Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Abu Dhabi</cbc:CityName>
            <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********23003</cbc:CompanyID>
            <!--  IBT-048 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-047, BTAE-16, BTAE-11 -->
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingCustomerParty>
   <cac:PayeeParty>
      <cac:PartyName>
         <cbc:Name>Payee Name</cbc:Name>
      </cac:PartyName>
      <cac:PartyLegalEntity>
         <cbc:CompanyID>**********</cbc:CompanyID>
      </cac:PartyLegalEntity>
   </cac:PayeeParty>
   <cac:PaymentMeans>
      <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
      <cac:CardAccount>
         <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
         <cbc:NetworkID>VISA</cbc:NetworkID>
         <cbc:HolderName>Card Holder Name</cbc:HolderName>
      </cac:CardAccount>
   </cac:PaymentMeans>
   <cac:PaymentTerms>
      <cbc:Note>Within a week</cbc:Note>
   </cac:PaymentTerms>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">262.15</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">419.44</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:TaxTotal>
      <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
      <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
      <cac:TaxSubtotal>
         <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
         <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:TaxSubtotal>
   </cac:TaxTotal>
   <cac:LegalMonetaryTotal>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
      <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
      <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
      <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
      <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
      <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
   </cac:LegalMonetaryTotal>
   <cac:InvoiceLine>
      <cbc:ID>1</cbc:ID>
      <cbc:Note>All items</cbc:Note>
      <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-01-31</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderLineReference>
         <cbc:LineID>1</cbc:LineID>
         <cac:OrderReference>
            <cbc:ID>PO-AE-220</cbc:ID>
         </cac:OrderReference>
      </cac:OrderLineReference>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">294</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">980</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
      </cac:TaxTotal>
      <cac:Item>
         <cbc:Description>Item Description</cbc:Description>
         <cbc:Name>Item Name</cbc:Name>
         <cac:BuyersItemIdentification>
            <cbc:ID>Purchase goods</cbc:ID>
         </cac:BuyersItemIdentification>
         <cac:SellersItemIdentification>
            <cbc:ID>Sales Goods</cbc:ID>
         </cac:SellersItemIdentification>
         <cac:AdditionalItemIdentification>
            <cbc:ID schemeID="SAC">3242423</cbc:ID>
         </cac:AdditionalItemIdentification>
         <cac:OriginCountry>
            <cbc:IdentificationCode>AE</cbc:IdentificationCode>
         </cac:OriginCountry>
         <cac:CommodityClassification>
            <cbc:CommodityCode>S</cbc:CommodityCode>
         </cac:CommodityClassification>
         <cac:ClassifiedTaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:ClassifiedTaxCategory>
         <cac:AdditionalItemProperty>
            <cbc:Name>Item details</cbc:Name>
            <cbc:Value>Item Value</cbc:Value>
         </cac:AdditionalItemProperty>
      </cac:Item>
      <cac:Price>
         <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
         <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:Amount currencyID="AED">0.1</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
         </cac:AllowanceCharge>
      </cac:Price>
   </cac:InvoiceLine>
</Invoice>

2025-06-26 12:42:09.624 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - ✅ Production AS4 send successful
2025-06-26 12:42:09.625 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 📊 Transaction ID: 85f0ae17-86eb-4449-9fd5-b429f3c87c98
2025-06-26 12:42:09.625 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 12:42:09.625 [http-nio-8080-exec-3] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 conversion completed in 44618 ms
2025-06-26 12:42:17.513 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-26 12:42:17.516 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-06-26 12:50:23.401 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 3280 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-26 12:50:23.406 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-26 12:50:23.408 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-26 12:50:26.424 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-26 12:50:26.440 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-26 12:50:30.983 [main] INFO  c.m.a.c.s.MultiCountryConfigService - Loading multi-country configuration...
2025-06-26 12:50:31.097 [main] INFO  c.m.a.c.s.MultiCountryConfigService - ✅ Multi-country configuration loaded successfully
2025-06-26 12:50:31.097 [main] INFO  c.m.a.c.s.MultiCountryConfigService - 📋 Configuration Summary:
2025-06-26 12:50:31.098 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DE (Germany): 3 document types
2025-06-26 12:50:31.101 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:50:31.102 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:50:31.102 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:50:31.103 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 FR (France): 3 document types
2025-06-26 12:50:31.103 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 12:50:31.103 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 12:50:31.104 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 12:50:31.104 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 IT (Italy): 3 document types
2025-06-26 12:50:31.104 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 12:50:31.105 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 12:50:31.105 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 12:50:31.105 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 NL (Netherlands): 3 document types
2025-06-26 12:50:31.106 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 12:50:31.106 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 12:50:31.106 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 12:50:31.107 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 ES (Spain): 3 document types
2025-06-26 12:50:31.107 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 12:50:31.107 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 12:50:31.107 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 12:50:31.107 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-26 12:50:31.108 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:50:31.108 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:50:31.108 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 12:50:31.126 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Country configuration loaded successfully
2025-06-26 12:50:31.129 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-26 12:50:31.219 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-26 12:50:31.270 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-26 12:50:31.276 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-26 12:50:31.317 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-26 12:50:31.317 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-26 12:50:31.318 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-26 12:50:31.318 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-26 12:50:31.319 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-26 12:50:31.319 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-26 12:50:31.319 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-26 12:50:31.320 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-26 12:50:31.326 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-26 12:50:31.327 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-26 12:50:31.327 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-26 12:50:31.327 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-26 12:50:31.327 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 12:50:31.327 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-26 12:50:31.327 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-26 12:50:31.334 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-26 12:50:31.334 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-26 12:50:31.334 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-26 12:50:31.377 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-26 12:50:31.377 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-26 12:50:31.383 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-26 12:50:31.385 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-26 12:50:31.606 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-26 12:50:31.607 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-26 12:50:31.698 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-26 12:50:32.284 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-26 12:50:32.285 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-26 12:50:32.299 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 10.109 seconds (process running for 11.111)
2025-06-26 12:54:21.187 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-26 12:54:21.190 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-06-26 15:43:47.759 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 15644 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-26 15:43:47.761 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-26 15:43:47.763 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-26 15:43:51.520 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-26 15:43:51.539 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-26 15:43:55.887 [main] INFO  c.m.a.c.s.MultiCountryConfigService - Loading multi-country configuration...
2025-06-26 15:43:55.994 [main] INFO  c.m.a.c.s.MultiCountryConfigService - ✅ Multi-country configuration loaded successfully
2025-06-26 15:43:55.995 [main] INFO  c.m.a.c.s.MultiCountryConfigService - 📋 Configuration Summary:
2025-06-26 15:43:55.996 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DE (Germany): 3 document types
2025-06-26 15:43:55.998 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:43:55.999 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:43:55.999 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:43:55.999 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 FR (France): 3 document types
2025-06-26 15:43:55.999 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 15:43:56.000 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 15:43:56.000 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 15:43:56.000 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 IT (Italy): 3 document types
2025-06-26 15:43:56.000 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 15:43:56.001 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 15:43:56.001 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 15:43:56.001 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 NL (Netherlands): 3 document types
2025-06-26 15:43:56.001 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 15:43:56.001 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 15:43:56.001 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 15:43:56.002 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 ES (Spain): 3 document types
2025-06-26 15:43:56.002 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 15:43:56.002 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 15:43:56.002 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 15:43:56.002 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-26 15:43:56.002 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:43:56.003 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:43:56.003 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:43:56.027 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Country configuration loaded successfully
2025-06-26 15:43:56.028 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-26 15:43:56.121 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-26 15:43:56.168 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-26 15:43:56.174 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-26 15:43:56.207 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-26 15:43:56.207 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-26 15:43:56.207 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-26 15:43:56.208 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-26 15:43:56.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-26 15:43:56.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-26 15:43:56.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-26 15:43:56.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-26 15:43:56.216 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-26 15:43:56.216 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-26 15:43:56.216 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-26 15:43:56.216 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-26 15:43:56.216 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 15:43:56.217 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-26 15:43:56.217 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-26 15:43:56.235 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-26 15:43:56.236 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-26 15:43:56.236 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-26 15:43:56.279 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-26 15:43:56.280 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-26 15:43:56.290 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-26 15:43:56.293 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-26 15:43:56.517 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-26 15:43:56.518 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-26 15:43:56.596 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-26 15:43:57.305 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-26 15:43:57.306 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-26 15:43:57.321 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 11.579 seconds (process running for 12.989)
2025-06-26 15:44:05.839 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-26 15:44:05.841 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-06-26 15:44:13.621 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 12588 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-26 15:44:13.624 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-26 15:44:13.627 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-26 15:44:16.437 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-26 15:44:16.451 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-26 15:44:20.974 [main] INFO  c.m.a.c.s.MultiCountryConfigService - Loading multi-country configuration...
2025-06-26 15:44:21.086 [main] INFO  c.m.a.c.s.MultiCountryConfigService - ✅ Multi-country configuration loaded successfully
2025-06-26 15:44:21.086 [main] INFO  c.m.a.c.s.MultiCountryConfigService - 📋 Configuration Summary:
2025-06-26 15:44:21.087 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DE (Germany): 3 document types
2025-06-26 15:44:21.092 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:44:21.092 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:44:21.092 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:44:21.092 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 FR (France): 3 document types
2025-06-26 15:44:21.093 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 15:44:21.093 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 15:44:21.093 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 15:44:21.093 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 IT (Italy): 3 document types
2025-06-26 15:44:21.093 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 15:44:21.093 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 15:44:21.093 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 15:44:21.094 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 NL (Netherlands): 3 document types
2025-06-26 15:44:21.094 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 15:44:21.094 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 15:44:21.094 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 15:44:21.094 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 ES (Spain): 3 document types
2025-06-26 15:44:21.095 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 15:44:21.095 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 15:44:21.095 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 15:44:21.095 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-26 15:44:21.095 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:44:21.095 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:44:21.095 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 15:44:21.117 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Country configuration loaded successfully
2025-06-26 15:44:21.118 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-26 15:44:21.212 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-26 15:44:21.275 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-26 15:44:21.286 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-26 15:44:21.332 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-26 15:44:21.332 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-26 15:44:21.333 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-26 15:44:21.333 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-26 15:44:21.334 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-26 15:44:21.334 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-26 15:44:21.334 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-26 15:44:21.335 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-26 15:44:21.343 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-26 15:44:21.343 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-26 15:44:21.343 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-26 15:44:21.343 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-26 15:44:21.343 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 15:44:21.343 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-26 15:44:21.343 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-26 15:44:21.364 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-26 15:44:21.365 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-26 15:44:21.365 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-26 15:44:21.412 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-26 15:44:21.413 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-26 15:44:21.422 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-26 15:44:21.426 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-26 15:44:21.690 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-26 15:44:21.696 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-26 15:44:21.787 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-26 15:44:22.549 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-26 15:44:22.549 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-26 15:44:22.565 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 10.067 seconds (process running for 11.043)
2025-06-26 15:44:25.263 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PEPPOL-EN16931-UBL.sch
2025-06-26 15:44:26.036 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-26 15:44:26.037 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - === Starting AS4 Conversion ===
2025-06-26 15:44:26.037 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Transaction ID: 125535c3-ba37-4529-8072-71b7c6a1abeb
2025-06-26 15:44:26.037 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Mode: dummy, Security: true
2025-06-26 15:44:26.037 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Extracting AS4 metadata from UBL XML
2025-06-26 15:44:26.064 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Found country code in address: AE
2025-06-26 15:44:26.064 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📄 Document type: INVOICE, Country: AE
2025-06-26 15:44:26.064 [http-nio-8080-exec-2] WARN  c.m.a.c.CountryConfigurationService - ⚠️ Country AE not found, using DEFAULT configuration
2025-06-26 15:44:26.065 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingSupplierParty: **********
2025-06-26 15:44:26.065 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingSupplierParty: 0235
2025-06-26 15:44:26.100 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 Extracted sender participant ID: ********** with scheme: 0235
2025-06-26 15:44:26.101 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingCustomerParty: **********
2025-06-26 15:44:26.101 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingCustomerParty: 0235
2025-06-26 15:44:26.101 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 Extracted receiver participant ID: ********** with scheme: 0235
2025-06-26 15:44:26.105 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Using document type ID: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1
2025-06-26 15:44:35.761 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Extracted ProfileExecutionID: ********
2025-06-26 15:44:35.761 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📅 Extracted IssueDate: 2025-02-06
2025-06-26 15:44:35.762 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 metadata extracted successfully
2025-06-26 15:44:35.763 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📄 Invoice ID: AE-01TEST
2025-06-26 15:44:35.763 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📊 Message ID: MSG-1750932875762
2025-06-26 15:44:35.763 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 Sender: [PeppolParticipantIdentifier@0x7877d549: scheme=iso6523-actorid-upis; value=**********]
2025-06-26 15:44:35.766 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 Receiver: [PeppolParticipantIdentifier@0x593b16cf: scheme=iso6523-actorid-upis; value=**********]
2025-06-26 15:44:35.766 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Document Type: [PeppolDocumentTypeIdentifier@0x1e67e891: scheme=busdox-docid-qns; value=urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1]
2025-06-26 15:44:35.767 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ⚙️ Process: [PeppolProcessIdentifier@0x19291873: scheme=cenbii-procid-ubl; value=urn:peppol:bis:billing]
2025-06-26 15:44:35.767 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Country Code: BE
2025-06-26 15:44:35.767 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Validating AS4 message metadata...
2025-06-26 15:44:35.767 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 message validation passed
2025-06-26 15:44:35.767 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION/TEST MODE: Using Phase4PeppolSender
2025-06-26 15:44:35.767 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 Sending AS4 message in dummy mode
2025-06-26 15:44:35.767 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Setting country code for SBDH: BE
2025-06-26 15:44:35.768 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 From Party ID: **********
2025-06-26 15:44:35.768 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 To Party ID: **********
2025-06-26 15:44:35.768 [http-nio-8080-exec-2] WARN  c.m.a.c.CountryConfigurationService - ⚠️ Country BE not found, using DEFAULT configuration
2025-06-26 15:44:50.999 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Custom SBDH created with:
2025-06-26 15:44:51.001 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    📋 Instance ID: 818ebebd-ed26-49f0-af19-0aee277c42d2
2025-06-26 15:44:51.002 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    📅 Creation Date: 2025-02-06T15:44:46.444760200
2025-06-26 15:44:51.004 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    👤 Sender: 0235:**********
2025-06-26 15:44:51.007 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    👥 Receiver: 0235:**********
2025-06-26 15:44:51.010 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    🌍 Country: BE
2025-06-26 15:44:51.331 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - Configuring Phase4PeppolSender with crypto settings for mode: dummy
2025-06-26 15:44:51.332 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Using configured crypto factory for signing
2025-06-26 15:44:51.332 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Crypto factory will use key alias: cert
2025-06-26 15:44:51.332 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - 🧪 DUMMY MODE: Configuring sender for testing with relaxed validation
2025-06-26 15:44:51.332 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Phase4PeppolSender configured with crypto settings
2025-06-26 15:44:51.332 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Crypto factory configured: ✅ Available
2025-06-26 15:44:51.334 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-26 15:44:51.335 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION MODE: Using real Peppol network
2025-06-26 15:44:51.335 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE with direct endpoint - FOR TESTING ONLY
2025-06-26 15:44:51.335 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Using direct endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 15:44:51.335 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE: Using direct endpoint with certificate validation BYPASSED
2025-06-26 15:44:51.336 [http-nio-8080-exec-2] WARN  c.m.a.c.s.TrustAllEndpointDetailProvider - ⚠️ TrustAllEndpointDetailProvider created - Certificate validation BYPASSED for endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 15:44:51.336 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-26 15:44:51.336 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Certificate validation BYPASSED for development/testing
2025-06-26 15:44:51.339 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Initializing TrustAllEndpointDetailProvider for participant: [PeppolParticipantIdentifier@0x593b16cf: scheme=iso6523-actorid-upis; value=**********]
2025-06-26 15:44:51.339 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate for receiver AP to bypass validation
2025-06-26 15:44:51.339 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate from keystore for receiver AP
2025-06-26 15:44:51.342 [http-nio-8080-exec-2] INFO  c.m.a.c.s.TrustAllEndpointDetailProvider - ✅ Dummy certificate loaded for receiver AP - using same cert as sender
2025-06-26 15:44:51.342 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-26 15:44:51.342 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-26 15:44:52.702 [http-nio-8080-exec-2] WARN  c.h.p.u.CertificateRevocationChecker - OCSP/CRL revocation check took 1289 milliseconds which is too long
2025-06-26 15:44:52.706 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Getting receiver AP endpoint URL: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 15:44:53.654 [http-nio-8080-exec-2] WARN  o.a.j.x.d.internal.dom.DOMReference - The input bytes to the digest operation are null. This may be due to a problem with the Reference URI or its Transforms.
2025-06-26 15:44:54.171 [http-nio-8080-exec-3] INFO  c.m.a.a.c.ReverseFlowController - 🔄 AS4 message endpoint called - redirecting to Phase4 servlet
2025-06-26 15:44:54.206 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - === AS4 Servlet: Processing incoming AS4 message ===
2025-06-26 15:44:54.206 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📨 Request URI: /reverse-flow/as4/
2025-06-26 15:44:54.206 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📋 Content-Type: multipart/related;    boundary="----=_Part_0_222181542.1750932893791";    type="application/soap+xml"; charset=UTF-8
2025-06-26 15:44:54.206 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📏 Content-Length: 15160
2025-06-26 15:44:54.206 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Method: POST
2025-06-26 15:44:54.211 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📦 Received AS4 message, length: 15021
2025-06-26 15:44:54.211 [http-nio-8080-exec-4] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔍 Extracting MessageId from AS4 message
2025-06-26 15:44:54.212 [http-nio-8080-exec-4] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ Found MessageId: fa157aaa-4b72-4611-8a1a-62abfd572f52@phase4
2025-06-26 15:44:55.747 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - === Processing AS4 message content ===
2025-06-26 15:44:55.747 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📦 AS4 message length: 15021
2025-06-26 15:44:55.748 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 AS4 message preview: 
------=_Part_0_222181542.1750932893791
Content-Type: application/soap+xml;charset=UTF-8
Content-Transfer-Encoding: binary

<?xml version="1.0" encoding="UTF-8"?><S12:Envelope xmlns:S12="http://www.w3.org/2003/05/soap-envelope"><S12:Header><wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" S12:mustUnderstand="true"><wsse:BinarySecurityToke
2025-06-26 15:44:57.923 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Original UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>818ebebd-ed26-49f0-af19-0aee277c42d2</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-02-06T15:44:46.444760200</sh:CreationDateAndTime>
      </sh:Document
2025-06-26 15:44:57.923 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Checking if UBL XML contains SBDH headers
2025-06-26 15:44:57.928 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📋 SBDH headers detected, extracting invoice document
2025-06-26 15:44:57.928 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Root element namespace: http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader
2025-06-26 15:44:57.929 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Found 1 Invoice nodes
2025-06-26 15:44:57.930 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Invoice node namespace: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2, local name: Invoice
2025-06-26 15:44:57.967 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔧 Converted node to XML string, length: 11967
2025-06-26 15:44:57.968 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-26 15:44:57.968 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted Invoice document from SBDH, length: 11967
2025-06-26 15:44:57.968 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted clean UBL document, length: 11967
2025-06-26 15:44:57.968 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Clean UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-26 15:44:57.968 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting country code from UBL XML
2025-06-26 15:44:57.972 [http-nio-8080-exec-4] WARN  c.m.a.c.s.Phase4AS4ReceiverService - ⚠️ No country code found in UBL XML, using DEFAULT
2025-06-26 15:44:57.972 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting document type from UBL XML
2025-06-26 15:44:57.974 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Detected document type: INVOICE from root element: Invoice
2025-06-26 15:44:57.975 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 🌍 Detected country: DEFAULT, document type: INVOICE
2025-06-26 15:44:57.975 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - Processing incoming UBL XML for country: DEFAULT and document type: INVOICE
2025-06-26 15:44:57.975 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - Parsing UBL XML to object using country-specific configuration: DEFAULT/INVOICE
2025-06-26 15:44:57.975 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.MultiCountryConfigService - Using configuration for DEFAULT/INVOICE: DocumentTypeConfig{classPath='oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType', factoryPath='oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory', schematronFile='PEPPOL-EN16931-UBL.sch', customizationId='urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0', profileId='urn:fdc:peppol.eu:2017:poacc:billing:01:1.0'}
2025-06-26 15:44:57.981 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 🔍 Using UBL class for parsing: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-06-26 15:44:57.981 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 UBL XML to parse (first 1000 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-26 15:44:57.981 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 Root element: <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
2025-06-26 15:44:57.982 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - ✅ Found expected root element: Invoice
2025-06-26 15:44:57.982 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating JAXB context for class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
2025-06-26 15:44:57.983 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-26 15:44:57.985 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-26 15:44:58.029 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-06-26 15:44:58.097 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-06-26 15:44:58.099 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-06-26 15:44:58.101 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-26 15:44:58.101 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-26 15:44:59.103 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 XML to unmarshal (first 500 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-26 15:44:59.127 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Successfully unmarshalled XML to object of type: jakarta.xml.bind.JAXBElement
2025-06-26 15:44:59.127 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 🔄 Handling response for DEFAULT/INVOICE with file writing as default
2025-06-26 15:44:59.132 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 📄 Response written to file: C:\Users\<USER>\backy\as4-main\responses\response_DEFAULT_INVOICE_20250626_154459_127.xml
2025-06-26 15:44:59.133 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 📊 File size: 11967 bytes
2025-06-26 15:44:59.133 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - Extracting sender ID from UBL document: JAXBElement
2025-06-26 15:44:59.133 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - No sender ID found in UBL document
2025-06-26 15:44:59.133 [http-nio-8080-exec-4] WARN  c.m.apsp.core.service.InvoiceService - ⚠️ No sender ID found in UBL document, cannot send response
2025-06-26 15:44:59.133 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - ✅ Response successfully written to file and sent via REST
2025-06-26 15:44:59.133 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - ✅ AS4 message processed successfully
2025-06-26 15:44:59.133 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 message processed successfully: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDate>
   <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
   <cbc:Note>Tax invoice</cbc:Note>
   <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
   <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
   <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
   <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
   <cac:InvoicePeriod>
      <cbc:StartDate>2025-01-31</cbc:StartDate>
      <cbc:EndDate>2025-02-06</cbc:EndDate>
   </cac:InvoicePeriod>
   <cac:OrderReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
   </cac:OrderReference>
   <cac:BillingReference>
      <cac:InvoiceDocumentReference>
         <cbc:ID>INV-234-2025</cbc:ID>
         <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      </cac:InvoiceDocumentReference>
   </cac:BillingReference>
   <cac:DespatchDocumentReference>
      <cbc:ID>Memo-1000</cbc:ID>
   </cac:DespatchDocumentReference>
   <cac:OriginatorDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
   </cac:OriginatorDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cac:ExternalReference>
            <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
         </cac:ExternalReference>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:ProjectReference>
      <cbc:ID>Regular work</cbc:ID>
   </cac:ProjectReference>
   <cac:AccountingSupplierParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Party Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Sharjah</cbc:CityName>
            <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********02003</cbc:CompanyID>
            <!--  IBT-031 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-030, BTAE-15, BTAE-12 -->
            <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingSupplierParty>
   <cac:AccountingCustomerParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Buyer Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Abu Dhabi</cbc:CityName>
            <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********23003</cbc:CompanyID>
            <!--  IBT-048 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-047, BTAE-16, BTAE-11 -->
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingCustomerParty>
   <cac:PayeeParty>
      <cac:PartyName>
         <cbc:Name>Payee Name</cbc:Name>
      </cac:PartyName>
      <cac:PartyLegalEntity>
         <cbc:CompanyID>**********</cbc:CompanyID>
      </cac:PartyLegalEntity>
   </cac:PayeeParty>
   <cac:PaymentMeans>
      <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
      <cac:CardAccount>
         <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
         <cbc:NetworkID>VISA</cbc:NetworkID>
         <cbc:HolderName>Card Holder Name</cbc:HolderName>
      </cac:CardAccount>
   </cac:PaymentMeans>
   <cac:PaymentTerms>
      <cbc:Note>Within a week</cbc:Note>
   </cac:PaymentTerms>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">262.15</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">419.44</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:TaxTotal>
      <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
      <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
      <cac:TaxSubtotal>
         <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
         <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:TaxSubtotal>
   </cac:TaxTotal>
   <cac:LegalMonetaryTotal>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
      <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
      <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
      <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
      <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
      <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
   </cac:LegalMonetaryTotal>
   <cac:InvoiceLine>
      <cbc:ID>1</cbc:ID>
      <cbc:Note>All items</cbc:Note>
      <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-01-31</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderLineReference>
         <cbc:LineID>1</cbc:LineID>
         <cac:OrderReference>
            <cbc:ID>PO-AE-220</cbc:ID>
         </cac:OrderReference>
      </cac:OrderLineReference>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">294</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">980</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
      </cac:TaxTotal>
      <cac:Item>
         <cbc:Description>Item Description</cbc:Description>
         <cbc:Name>Item Name</cbc:Name>
         <cac:BuyersItemIdentification>
            <cbc:ID>Purchase goods</cbc:ID>
         </cac:BuyersItemIdentification>
         <cac:SellersItemIdentification>
            <cbc:ID>Sales Goods</cbc:ID>
         </cac:SellersItemIdentification>
         <cac:AdditionalItemIdentification>
            <cbc:ID schemeID="SAC">3242423</cbc:ID>
         </cac:AdditionalItemIdentification>
         <cac:OriginCountry>
            <cbc:IdentificationCode>AE</cbc:IdentificationCode>
         </cac:OriginCountry>
         <cac:CommodityClassification>
            <cbc:CommodityCode>S</cbc:CommodityCode>
         </cac:CommodityClassification>
         <cac:ClassifiedTaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:ClassifiedTaxCategory>
         <cac:AdditionalItemProperty>
            <cbc:Name>Item details</cbc:Name>
            <cbc:Value>Item Value</cbc:Value>
         </cac:AdditionalItemProperty>
      </cac:Item>
      <cac:Price>
         <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
         <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:Amount currencyID="AED">0.1</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
         </cac:AllowanceCharge>
      </cac:Price>
   </cac:InvoiceLine>
</Invoice>

2025-06-26 15:45:03.214 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Production AS4 send successful
2025-06-26 15:45:03.214 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📊 Transaction ID: 125535c3-ba37-4529-8072-71b7c6a1abeb
2025-06-26 15:45:03.214 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 15:45:03.215 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 conversion completed in 37178 ms
2025-06-26 15:45:07.316 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-26 15:45:07.318 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-06-26 17:08:58.409 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 16492 (C:\Users\<USER>\backy\as4-main\target\classes started by kushagrat in C:\Users\<USER>\backy\as4-main)
2025-06-26 17:08:58.413 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-06-26 17:08:58.415 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-06-26 17:09:01.388 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-06-26 17:09:01.398 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-06-26 17:09:06.295 [main] INFO  c.m.a.c.s.MultiCountryConfigService - Loading multi-country configuration...
2025-06-26 17:09:06.401 [main] INFO  c.m.a.c.s.MultiCountryConfigService - ✅ Multi-country configuration loaded successfully
2025-06-26 17:09:06.402 [main] INFO  c.m.a.c.s.MultiCountryConfigService - 📋 Configuration Summary:
2025-06-26 17:09:06.403 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DE (Germany): 3 document types
2025-06-26 17:09:06.406 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 17:09:06.407 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 17:09:06.408 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 17:09:06.408 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 FR (France): 3 document types
2025-06-26 17:09:06.408 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 17:09:06.409 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 17:09:06.409 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-06-26 17:09:06.409 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 IT (Italy): 3 document types
2025-06-26 17:09:06.409 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 17:09:06.410 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 17:09:06.410 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-06-26 17:09:06.410 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 NL (Netherlands): 3 document types
2025-06-26 17:09:06.410 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 17:09:06.410 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 17:09:06.410 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-06-26 17:09:06.410 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 ES (Spain): 3 document types
2025-06-26 17:09:06.411 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 17:09:06.411 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 17:09:06.411 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-06-26 17:09:06.411 [main] INFO  c.m.a.c.s.MultiCountryConfigService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-06-26 17:09:06.411 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-06-26 17:09:06.411 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-06-26 17:09:06.411 [main] DEBUG c.m.a.c.s.MultiCountryConfigService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-06-26 17:09:06.436 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Country configuration loaded successfully
2025-06-26 17:09:06.437 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-06-26 17:09:06.544 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-06-26 17:09:06.602 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded: keystore/cert.p12
2025-06-26 17:09:06.607 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-06-26 17:09:06.648 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-06-26 17:09:06.649 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-06-26 17:09:06.649 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-26 17:09:06.649 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-06-26 17:09:06.650 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-06-26 17:09:06.650 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: keystore/cert.p12
2025-06-26 17:09:06.650 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-06-26 17:09:06.651 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: keystore/cert.p12
2025-06-26 17:09:06.658 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-06-26 17:09:06.658 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-06-26 17:09:06.658 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-06-26 17:09:06.658 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-06-26 17:09:06.658 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 17:09:06.658 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-06-26 17:09:06.659 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found: keystore/cert.p12
2025-06-26 17:09:06.699 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-06-26 17:09:06.700 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-06-26 17:09:06.700 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found: keystore/cert.p12
2025-06-26 17:09:06.744 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-06-26 17:09:06.744 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-06-26 17:09:06.772 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-06-26 17:09:06.781 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-26 17:09:06.961 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-06-26 17:09:06.961 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-06-26 17:09:07.032 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-26 17:09:07.617 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-06-26 17:09:07.618 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-06-26 17:09:07.632 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 10.487 seconds (process running for 11.548)
2025-06-26 17:09:12.879 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PEPPOL-EN16931-UBL.sch
2025-06-26 17:09:13.700 [http-nio-8080-exec-2] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-26 17:09:13.700 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - === Starting AS4 Conversion ===
2025-06-26 17:09:13.700 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Transaction ID: 6fb4083b-c906-4c9a-92fc-b3787d2b22f7
2025-06-26 17:09:13.700 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Mode: dummy, Security: true
2025-06-26 17:09:13.700 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - Extracting AS4 metadata from UBL XML
2025-06-26 17:09:13.728 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Found country code in address: AE
2025-06-26 17:09:13.728 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📄 Document type: INVOICE, Country: AE
2025-06-26 17:09:13.728 [http-nio-8080-exec-2] WARN  c.m.a.c.CountryConfigurationService - ⚠️ Country AE not found, using DEFAULT configuration
2025-06-26 17:09:13.729 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingSupplierParty: **********
2025-06-26 17:09:13.730 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingSupplierParty: 0235
2025-06-26 17:09:13.761 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 Extracted sender participant ID: ********** with scheme: 0235
2025-06-26 17:09:13.762 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingCustomerParty: **********
2025-06-26 17:09:13.762 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingCustomerParty: 0235
2025-06-26 17:09:13.762 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 Extracted receiver participant ID: ********** with scheme: 0235
2025-06-26 17:09:13.766 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Using document type ID: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1
2025-06-26 17:09:21.368 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Extracted ProfileExecutionID: ********
2025-06-26 17:09:21.369 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📅 Extracted IssueDate: 2025-02-06
2025-06-26 17:09:21.370 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 metadata extracted successfully
2025-06-26 17:09:21.370 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📄 Invoice ID: AE-01TEST
2025-06-26 17:09:21.370 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📊 Message ID: MSG-1750937961370
2025-06-26 17:09:21.370 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 Sender: [PeppolParticipantIdentifier@0x59c2af5a: scheme=iso6523-actorid-upis; value=**********]
2025-06-26 17:09:21.373 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 Receiver: [PeppolParticipantIdentifier@0x48d89316: scheme=iso6523-actorid-upis; value=**********]
2025-06-26 17:09:21.373 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📋 Document Type: [PeppolDocumentTypeIdentifier@0x118bc97b: scheme=busdox-docid-qns; value=urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1]
2025-06-26 17:09:21.373 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ⚙️ Process: [PeppolProcessIdentifier@0x427244ab: scheme=cenbii-procid-ubl; value=urn:peppol:bis:billing]
2025-06-26 17:09:21.373 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Country Code: BE
2025-06-26 17:09:21.373 [http-nio-8080-exec-2] DEBUG c.m.a.c.service.AS4ConversionService - Validating AS4 message metadata...
2025-06-26 17:09:21.374 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 message validation passed
2025-06-26 17:09:21.374 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION/TEST MODE: Using Phase4PeppolSender
2025-06-26 17:09:21.374 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 Sending AS4 message in dummy mode
2025-06-26 17:09:21.374 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Setting country code for SBDH: BE
2025-06-26 17:09:21.374 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👤 From Party ID: **********
2025-06-26 17:09:21.374 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 👥 To Party ID: **********
2025-06-26 17:09:21.374 [http-nio-8080-exec-2] WARN  c.m.a.c.CountryConfigurationService - ⚠️ Country BE not found, using DEFAULT configuration
2025-06-26 17:09:23.069 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Custom SBDH created with:
2025-06-26 17:09:23.069 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    📋 Instance ID: 6e1a5118-d34d-45a8-97ba-4fff20767398
2025-06-26 17:09:23.069 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    📅 Creation Date: 2025-02-06T17:09:22.990140600
2025-06-26 17:09:23.069 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    👤 Sender: 0235:**********
2025-06-26 17:09:23.070 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    👥 Receiver: 0235:**********
2025-06-26 17:09:23.070 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService -    🌍 Country: BE
2025-06-26 17:09:23.394 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - Configuring Phase4PeppolSender with crypto settings for mode: dummy
2025-06-26 17:09:23.395 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Using configured crypto factory for signing
2025-06-26 17:09:23.395 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Crypto factory will use key alias: cert
2025-06-26 17:09:23.395 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - 🧪 DUMMY MODE: Configuring sender for testing with relaxed validation
2025-06-26 17:09:23.395 [http-nio-8080-exec-2] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Phase4PeppolSender configured with crypto settings
2025-06-26 17:09:23.395 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Crypto factory configured: ✅ Available
2025-06-26 17:09:23.402 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Keystore info: Keystore: keystore/cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-06-26 17:09:23.402 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION MODE: Using real Peppol network
2025-06-26 17:09:23.402 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE with direct endpoint - FOR TESTING ONLY
2025-06-26 17:09:23.402 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Using direct endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 17:09:23.402 [http-nio-8080-exec-2] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE: Using direct endpoint with certificate validation BYPASSED
2025-06-26 17:09:23.403 [http-nio-8080-exec-2] WARN  c.m.a.c.s.TrustAllEndpointDetailProvider - ⚠️ TrustAllEndpointDetailProvider created - Certificate validation BYPASSED for endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 17:09:23.404 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-26 17:09:23.404 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Certificate validation BYPASSED for development/testing
2025-06-26 17:09:23.408 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Initializing TrustAllEndpointDetailProvider for participant: [PeppolParticipantIdentifier@0x48d89316: scheme=iso6523-actorid-upis; value=**********]
2025-06-26 17:09:23.408 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate for receiver AP to bypass validation
2025-06-26 17:09:23.408 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate from keystore for receiver AP
2025-06-26 17:09:23.413 [http-nio-8080-exec-2] INFO  c.m.a.c.s.TrustAllEndpointDetailProvider - ✅ Dummy certificate loaded for receiver AP - using same cert as sender
2025-06-26 17:09:23.414 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-06-26 17:09:23.414 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-06-26 17:09:24.687 [http-nio-8080-exec-2] WARN  c.h.p.u.CertificateRevocationChecker - OCSP/CRL revocation check took 1198 milliseconds which is too long
2025-06-26 17:09:24.690 [http-nio-8080-exec-2] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Getting receiver AP endpoint URL: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 17:09:25.573 [http-nio-8080-exec-2] WARN  o.a.j.x.d.internal.dom.DOMReference - The input bytes to the digest operation are null. This may be due to a problem with the Reference URI or its Transforms.
2025-06-26 17:09:26.082 [http-nio-8080-exec-3] INFO  c.m.a.a.c.ReverseFlowController - 🔄 AS4 message endpoint called - redirecting to Phase4 servlet
2025-06-26 17:09:26.117 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - === AS4 Servlet: Processing incoming AS4 message ===
2025-06-26 17:09:26.118 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📨 Request URI: /reverse-flow/as4/
2025-06-26 17:09:26.118 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📋 Content-Type: multipart/related;    boundary="----=_Part_0_188622787.1750937965707";    type="application/soap+xml"; charset=UTF-8
2025-06-26 17:09:26.118 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📏 Content-Length: 15161
2025-06-26 17:09:26.118 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Method: POST
2025-06-26 17:09:26.119 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 📦 Received AS4 message, length: 15003
2025-06-26 17:09:26.120 [http-nio-8080-exec-4] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔍 Extracting MessageId from AS4 message
2025-06-26 17:09:26.120 [http-nio-8080-exec-4] DEBUG c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ Found MessageId: dea5b313-adba-4b43-8925-e5794de1f529@phase4
2025-06-26 17:09:27.629 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - === Processing AS4 message content ===
2025-06-26 17:09:27.629 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📦 AS4 message length: 15003
2025-06-26 17:09:27.630 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 AS4 message preview: 
------=_Part_0_188622787.1750937965707
Content-Type: application/soap+xml;charset=UTF-8
Content-Transfer-Encoding: binary

<?xml version="1.0" encoding="UTF-8"?><S12:Envelope xmlns:S12="http://www.w3.org/2003/05/soap-envelope"><S12:Header><wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" S12:mustUnderstand="true"><wsse:BinarySecurityToke
2025-06-26 17:09:27.675 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Original UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>6e1a5118-d34d-45a8-97ba-4fff20767398</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-02-06T17:09:22.990140600</sh:CreationDateAndTime>
      </sh:Document
2025-06-26 17:09:27.675 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Checking if UBL XML contains SBDH headers
2025-06-26 17:09:27.679 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📋 SBDH headers detected, extracting invoice document
2025-06-26 17:09:27.679 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Root element namespace: http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader
2025-06-26 17:09:27.680 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Found 1 Invoice nodes
2025-06-26 17:09:27.681 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Invoice node namespace: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2, local name: Invoice
2025-06-26 17:09:27.718 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔧 Converted node to XML string, length: 11967
2025-06-26 17:09:27.718 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-26 17:09:27.718 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted Invoice document from SBDH, length: 11967
2025-06-26 17:09:27.718 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Extracted clean UBL document, length: 11967
2025-06-26 17:09:27.719 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - 🔍 Clean UBL XML preview: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-26 17:09:27.719 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting country code from UBL XML
2025-06-26 17:09:27.722 [http-nio-8080-exec-4] WARN  c.m.a.c.s.Phase4AS4ReceiverService - ⚠️ No country code found in UBL XML, using DEFAULT
2025-06-26 17:09:27.722 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.Phase4AS4ReceiverService - Extracting document type from UBL XML
2025-06-26 17:09:27.725 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 📄 Detected document type: INVOICE from root element: Invoice
2025-06-26 17:09:27.725 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - 🌍 Detected country: DEFAULT, document type: INVOICE
2025-06-26 17:09:27.725 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - Processing incoming UBL XML for country: DEFAULT and document type: INVOICE
2025-06-26 17:09:27.725 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - Parsing UBL XML to object using country-specific configuration: DEFAULT/INVOICE
2025-06-26 17:09:27.725 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.MultiCountryConfigService - Using configuration for DEFAULT/INVOICE: DocumentTypeConfig{classPath='oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType', factoryPath='oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory', schematronFile='PEPPOL-EN16931-UBL.sch', customizationId='urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0', profileId='urn:fdc:peppol.eu:2017:poacc:billing:01:1.0'}
2025-06-26 17:09:27.728 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 🔍 Using UBL class for parsing: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-06-26 17:09:27.728 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 UBL XML to parse (first 1000 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDa
2025-06-26 17:09:27.728 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - 🔍 Root element: <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
2025-06-26 17:09:27.731 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - ✅ Found expected root element: Invoice
2025-06-26 17:09:27.732 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating JAXB context for class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
2025-06-26 17:09:27.733 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-26 17:09:27.737 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-06-26 17:09:27.776 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-06-26 17:09:27.848 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-06-26 17:09:27.849 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-06-26 17:09:27.850 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-26 17:09:27.850 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-06-26 17:09:28.987 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 XML to unmarshal (first 500 chars): <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specifi
2025-06-26 17:09:29.015 [http-nio-8080-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Successfully unmarshalled XML to object of type: jakarta.xml.bind.JAXBElement
2025-06-26 17:09:29.015 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 🔄 Handling response for DEFAULT/INVOICE with file writing as default
2025-06-26 17:09:29.022 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 📄 Response written to file: C:\Users\<USER>\backy\as4-main\responses\response_DEFAULT_INVOICE_20250626_170929_016.xml
2025-06-26 17:09:29.022 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - 📊 File size: 11967 bytes
2025-06-26 17:09:29.022 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - Extracting sender ID from UBL document: JAXBElement
2025-06-26 17:09:29.023 [http-nio-8080-exec-4] DEBUG c.m.apsp.core.service.InvoiceService - No sender ID found in UBL document
2025-06-26 17:09:29.023 [http-nio-8080-exec-4] WARN  c.m.apsp.core.service.InvoiceService - ⚠️ No sender ID found in UBL document, cannot send response
2025-06-26 17:09:29.023 [http-nio-8080-exec-4] INFO  c.m.apsp.core.service.InvoiceService - ✅ Response successfully written to file and sent via REST
2025-06-26 17:09:29.023 [http-nio-8080-exec-4] INFO  c.m.a.c.s.Phase4AS4ReceiverService - ✅ AS4 message processed successfully
2025-06-26 17:09:29.023 [http-nio-8080-exec-4] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 message processed successfully: <?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDate>
   <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
   <cbc:Note>Tax invoice</cbc:Note>
   <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
   <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
   <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
   <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
   <cac:InvoicePeriod>
      <cbc:StartDate>2025-01-31</cbc:StartDate>
      <cbc:EndDate>2025-02-06</cbc:EndDate>
   </cac:InvoicePeriod>
   <cac:OrderReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
   </cac:OrderReference>
   <cac:BillingReference>
      <cac:InvoiceDocumentReference>
         <cbc:ID>INV-234-2025</cbc:ID>
         <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      </cac:InvoiceDocumentReference>
   </cac:BillingReference>
   <cac:DespatchDocumentReference>
      <cbc:ID>Memo-1000</cbc:ID>
   </cac:DespatchDocumentReference>
   <cac:OriginatorDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
   </cac:OriginatorDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cac:ExternalReference>
            <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
         </cac:ExternalReference>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:ProjectReference>
      <cbc:ID>Regular work</cbc:ID>
   </cac:ProjectReference>
   <cac:AccountingSupplierParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Party Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Sharjah</cbc:CityName>
            <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********02003</cbc:CompanyID>
            <!--  IBT-031 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-030, BTAE-15, BTAE-12 -->
            <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingSupplierParty>
   <cac:AccountingCustomerParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Buyer Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Abu Dhabi</cbc:CityName>
            <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********23003</cbc:CompanyID>
            <!--  IBT-048 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-047, BTAE-16, BTAE-11 -->
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingCustomerParty>
   <cac:PayeeParty>
      <cac:PartyName>
         <cbc:Name>Payee Name</cbc:Name>
      </cac:PartyName>
      <cac:PartyLegalEntity>
         <cbc:CompanyID>**********</cbc:CompanyID>
      </cac:PartyLegalEntity>
   </cac:PayeeParty>
   <cac:PaymentMeans>
      <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
      <cac:CardAccount>
         <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
         <cbc:NetworkID>VISA</cbc:NetworkID>
         <cbc:HolderName>Card Holder Name</cbc:HolderName>
      </cac:CardAccount>
   </cac:PaymentMeans>
   <cac:PaymentTerms>
      <cbc:Note>Within a week</cbc:Note>
   </cac:PaymentTerms>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">262.15</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">419.44</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:TaxTotal>
      <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
      <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
      <cac:TaxSubtotal>
         <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
         <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:TaxSubtotal>
   </cac:TaxTotal>
   <cac:LegalMonetaryTotal>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
      <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
      <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
      <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
      <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
      <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
   </cac:LegalMonetaryTotal>
   <cac:InvoiceLine>
      <cbc:ID>1</cbc:ID>
      <cbc:Note>All items</cbc:Note>
      <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-01-31</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderLineReference>
         <cbc:LineID>1</cbc:LineID>
         <cac:OrderReference>
            <cbc:ID>PO-AE-220</cbc:ID>
         </cac:OrderReference>
      </cac:OrderLineReference>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">294</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">980</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
      </cac:TaxTotal>
      <cac:Item>
         <cbc:Description>Item Description</cbc:Description>
         <cbc:Name>Item Name</cbc:Name>
         <cac:BuyersItemIdentification>
            <cbc:ID>Purchase goods</cbc:ID>
         </cac:BuyersItemIdentification>
         <cac:SellersItemIdentification>
            <cbc:ID>Sales Goods</cbc:ID>
         </cac:SellersItemIdentification>
         <cac:AdditionalItemIdentification>
            <cbc:ID schemeID="SAC">3242423</cbc:ID>
         </cac:AdditionalItemIdentification>
         <cac:OriginCountry>
            <cbc:IdentificationCode>AE</cbc:IdentificationCode>
         </cac:OriginCountry>
         <cac:CommodityClassification>
            <cbc:CommodityCode>S</cbc:CommodityCode>
         </cac:CommodityClassification>
         <cac:ClassifiedTaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:ClassifiedTaxCategory>
         <cac:AdditionalItemProperty>
            <cbc:Name>Item details</cbc:Name>
            <cbc:Value>Item Value</cbc:Value>
         </cac:AdditionalItemProperty>
      </cac:Item>
      <cac:Price>
         <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
         <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:Amount currencyID="AED">0.1</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
         </cac:AllowanceCharge>
      </cac:Price>
   </cac:InvoiceLine>
</Invoice>

2025-06-26 17:09:33.675 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ Production AS4 send successful
2025-06-26 17:09:33.675 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 📊 Transaction ID: 6fb4083b-c906-4c9a-92fc-b3787d2b22f7
2025-06-26 17:09:33.675 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Endpoint: http://localhost:8080/reverse-flow/receive-as4-message
2025-06-26 17:09:33.675 [http-nio-8080-exec-2] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 conversion completed in 19975 ms
2025-06-26 17:11:49.910 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m16s225ms513µs).
2025-06-26 17:11:49.917 [http-nio-8080-exec-8] DEBUG c.m.a.c.s.MultiCountryConfigService - Using configuration for DE/INVOICE: DocumentTypeConfig{classPath='oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType', factoryPath='oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory', schematronFile='PEPPOL-EN16931-UBL.sch', customizationId='urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0', profileId='urn:fdc:peppol.eu:2017:poacc:billing:01:1.0'}
2025-06-26 17:13:59.667 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m9s760ms493µs600ns).
2025-06-26 17:13:59.674 [http-nio-8080-exec-8] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PEPPOL-EN16931-UBL.sch
2025-06-26 17:13:59.773 [http-nio-8080-exec-8] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-26 17:14:15.923 [http-nio-8080-exec-9] DEBUG c.m.a.c.s.MultiCountryConfigService - Using configuration for DE/INVOICE: DocumentTypeConfig{classPath='oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType', factoryPath='oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory', schematronFile='PEPPOL-EN16931-UBL.sch', customizationId='urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0', profileId='urn:fdc:peppol.eu:2017:poacc:billing:01:1.0'}
2025-06-26 17:14:24.470 [http-nio-8080-exec-9] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PEPPOL-EN16931-UBL.sch
2025-06-26 17:14:24.551 [http-nio-8080-exec-9] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-26 17:15:03.349 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m3s682ms212µs700ns).
2025-06-26 17:15:03.356 [http-nio-8080-exec-10] DEBUG c.m.a.c.s.MultiCountryConfigService - Using configuration for DE/INVOICE: DocumentTypeConfig{classPath='oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType', factoryPath='oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory', schematronFile='PEPPOL-EN16931-UBL.sch', customizationId='urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0', profileId='urn:fdc:peppol.eu:2017:poacc:billing:01:1.0'}
2025-06-26 17:15:03.357 [http-nio-8080-exec-10] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PEPPOL-EN16931-UBL.sch
2025-06-26 17:15:03.378 [http-nio-8080-exec-10] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-06-26 17:15:03.460 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-06-26 17:15:03.464 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
