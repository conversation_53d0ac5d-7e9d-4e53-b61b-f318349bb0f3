@echo off
echo ========================================
echo Production-Ready AS4 Certificate Generator
echo ========================================
echo.

set KEYSTORE_DIR=src\main\resources\keystore
set KEYSTORE_PASS=changeit
set KEY_PASS=changeit
set VALIDITY=365

:: Create keystore directory if it doesn't exist
if not exist "%KEYSTORE_DIR%" mkdir "%KEYSTORE_DIR%"

echo This script generates DUMMY certificates for testing only.
echo For PRODUCTION use, you need real Peppol certificates!
echo.
echo Modes available:
echo - DUMMY: Self-signed certificates for testing
echo - TEST: Placeholder for Peppol test certificates
echo - PRODUCTION: Placeholder for Peppol production certificates
echo.

echo Generating DUMMY certificates...
echo.
echo 1. Generating AS4 keystore with private key and certificate...
keytool -genkeypair ^
    -alias as4-test ^
    -keyalg RSA ^
    -keysize 2048 ^
    -validity %VALIDITY% ^
    -keystore "%KEYSTORE_DIR%\as4-keystore.p12" ^
    -storetype PKCS12 ^
    -storepass %KEYSTORE_PASS% ^
    -keypass %KEY_PASS% ^
    -dname "CN=Phase4 Dummy Certificate, OU=Testing Unit, O=Dummy Organization, L=Test City, ST=Test State, C=US" ^
    -ext SAN=dns:localhost,ip:127.0.0.1,dns:*.test.local

if %ERRORLEVEL% neq 0 (
    echo Error generating keystore!
    pause
    exit /b 1
)

echo.
echo 2. Exporting certificate from keystore...
keytool -exportcert ^
    -alias as4-test ^
    -keystore "%KEYSTORE_DIR%\as4-keystore.p12" ^
    -storetype PKCS12 ^
    -storepass %KEYSTORE_PASS% ^
    -file "%KEYSTORE_DIR%\as4-test-cert.crt"

if %ERRORLEVEL% neq 0 (
    echo Error exporting certificate!
    pause
    exit /b 1
)

echo.
echo 3. Creating truststore and importing certificate...
keytool -importcert ^
    -alias as4-test ^
    -keystore "%KEYSTORE_DIR%\as4-truststore.p12" ^
    -storetype PKCS12 ^
    -storepass %KEYSTORE_PASS% ^
    -file "%KEYSTORE_DIR%\as4-test-cert.crt" ^
    -noprompt

if %ERRORLEVEL% neq 0 (
    echo Error creating truststore!
    pause
    exit /b 1
)

echo.
echo 4. Listing keystore contents...
keytool -list -keystore "%KEYSTORE_DIR%\as4-keystore.p12" -storetype PKCS12 -storepass %KEYSTORE_PASS%

echo.
echo 5. Listing truststore contents...
keytool -list -keystore "%KEYSTORE_DIR%\as4-truststore.p12" -storetype PKCS12 -storepass %KEYSTORE_PASS%

echo.
echo ========================================
echo DUMMY Certificate generation completed!
echo ========================================
echo.
echo Files created:
echo - %KEYSTORE_DIR%\as4-keystore.p12 (contains private key and certificate)
echo - %KEYSTORE_DIR%\as4-truststore.p12 (contains trusted certificates)
echo - %KEYSTORE_DIR%\as4-test-cert.crt (exported certificate)
echo.
echo Configuration:
echo - Keystore password: %KEYSTORE_PASS%
echo - Key password: %KEY_PASS%
echo - Key alias: as4-test
echo.
echo ========================================
echo IMPORTANT NOTES:
echo ========================================
echo.
echo ✅ DUMMY MODE: Ready for testing
echo   - Run: mvn spring-boot:run -Dspring.profiles.active=dummy
echo   - These certificates work for local testing only
echo.
echo ⚠️  TEST MODE: Requires Peppol test certificates
echo   - Get test certificates from Peppol Authority
echo   - Replace dummy certificates with real test certificates
echo   - Run: mvn spring-boot:run -Dspring.profiles.active=test
echo.
echo 🚨 PRODUCTION MODE: Requires Peppol production certificates
echo   - Get production certificates from authorized Peppol CA
echo   - Register as Peppol participant
echo   - Configure production environment variables
echo   - Run: mvn spring-boot:run -Dspring.profiles.active=production
echo.
echo Next steps:
echo 1. Test dummy mode: mvn spring-boot:run
echo 2. Check system status in logs
echo 3. Send test messages to verify functionality
echo.
pause
