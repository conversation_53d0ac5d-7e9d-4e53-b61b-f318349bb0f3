<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
   <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
   <!--  IBT-024 -->
   <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
   <!--  IBT-023 -->
   <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
   <cbc:ID>AE-01TEST</cbc:ID>
   <cbc:IssueDate>2025-02-06</cbc:IssueDate>
   <cbc:IssueTime>07:54:00</cbc:IssueTime>
   <cbc:DueDate>2025-02-13</cbc:DueDate>
   <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
   <cbc:Note>Tax invoice</cbc:Note>
   <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
   <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
   <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
   <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
   <cac:InvoicePeriod>
      <cbc:StartDate>2025-01-31</cbc:StartDate>
      <cbc:EndDate>2025-02-06</cbc:EndDate>
   </cac:InvoicePeriod>
   <cac:OrderReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
   </cac:OrderReference>
   <cac:BillingReference>
      <cac:InvoiceDocumentReference>
         <cbc:ID>INV-234-2025</cbc:ID>
         <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      </cac:InvoiceDocumentReference>
   </cac:BillingReference>
   <cac:DespatchDocumentReference>
      <cbc:ID>Memo-1000</cbc:ID>
   </cac:DespatchDocumentReference>
   <cac:OriginatorDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
   </cac:OriginatorDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cac:ExternalReference>
            <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
         </cac:ExternalReference>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:AdditionalDocumentReference>
      <cbc:ID>PO-AE-220</cbc:ID>
      <cac:Attachment>
         <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
      </cac:Attachment>
   </cac:AdditionalDocumentReference>
   <cac:ProjectReference>
      <cbc:ID>Regular work</cbc:ID>
   </cac:ProjectReference>
   <cac:AccountingSupplierParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Party Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Sharjah</cbc:CityName>
            <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********02003</cbc:CompanyID>
            <!--  IBT-031 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-030, BTAE-15, BTAE-12 -->
            <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingSupplierParty>
   <cac:AccountingCustomerParty>
      <cac:Party>
         <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
         <cac:PartyName>
            <cbc:Name>Buyer Trade Name</cbc:Name>
         </cac:PartyName>
         <cac:PostalAddress>
            <cbc:StreetName>Street Name</cbc:StreetName>
            <cbc:CityName>Abu Dhabi</cbc:CityName>
            <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
            <cac:Country>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:Country>
         </cac:PostalAddress>
         <cac:PartyTaxScheme>
            <cbc:CompanyID>**********23003</cbc:CompanyID>
            <!--  IBT-048 -->
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:PartyTaxScheme>
         <cac:PartyLegalEntity>
            <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
            <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
            <!--  IBT-047, BTAE-16, BTAE-11 -->
         </cac:PartyLegalEntity>
         <cac:Contact>
            <cbc:Name>Contact Name</cbc:Name>
            <cbc:Telephone>Telephone number</cbc:Telephone>
            <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
         </cac:Contact>
      </cac:Party>
   </cac:AccountingCustomerParty>
   <cac:PayeeParty>
      <cac:PartyName>
         <cbc:Name>Payee Name</cbc:Name>
      </cac:PartyName>
      <cac:PartyLegalEntity>
         <cbc:CompanyID>**********</cbc:CompanyID>
      </cac:PartyLegalEntity>
   </cac:PayeeParty>
   <cac:PaymentMeans>
      <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
      <cac:CardAccount>
         <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
         <cbc:NetworkID>VISA</cbc:NetworkID>
         <cbc:HolderName>Card Holder Name</cbc:HolderName>
      </cac:CardAccount>
   </cac:PaymentMeans>
   <cac:PaymentTerms>
      <cbc:Note>Within a week</cbc:Note>
   </cac:PaymentTerms>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">262.15</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:AllowanceCharge>
      <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
      <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="AED">419.44</cbc:Amount>
      <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
      <cac:TaxCategory>
         <cbc:ID>S</cbc:ID>
         <cbc:Percent>5</cbc:Percent>
         <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
         </cac:TaxScheme>
      </cac:TaxCategory>
   </cac:AllowanceCharge>
   <cac:TaxTotal>
      <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
      <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
      <cac:TaxSubtotal>
         <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
         <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:TaxSubtotal>
   </cac:TaxTotal>
   <cac:LegalMonetaryTotal>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
      <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
      <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
      <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
      <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
      <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
   </cac:LegalMonetaryTotal>
   <cac:InvoiceLine>
      <cbc:ID>1</cbc:ID>
      <cbc:Note>All items</cbc:Note>
      <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
      <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-01-31</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderLineReference>
         <cbc:LineID>1</cbc:LineID>
         <cac:OrderReference>
            <cbc:ID>PO-AE-220</cbc:ID>
         </cac:OrderReference>
      </cac:OrderLineReference>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">294</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">980</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
      </cac:TaxTotal>
      <cac:Item>
         <cbc:Description>Item Description</cbc:Description>
         <cbc:Name>Item Name</cbc:Name>
         <cac:BuyersItemIdentification>
            <cbc:ID>Purchase goods</cbc:ID>
         </cac:BuyersItemIdentification>
         <cac:SellersItemIdentification>
            <cbc:ID>Sales Goods</cbc:ID>
         </cac:SellersItemIdentification>
         <cac:AdditionalItemIdentification>
            <cbc:ID schemeID="SAC">3242423</cbc:ID>
         </cac:AdditionalItemIdentification>
         <cac:OriginCountry>
            <cbc:IdentificationCode>AE</cbc:IdentificationCode>
         </cac:OriginCountry>
         <cac:CommodityClassification>
            <cbc:CommodityCode>S</cbc:CommodityCode>
         </cac:CommodityClassification>
         <cac:ClassifiedTaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:ClassifiedTaxCategory>
         <cac:AdditionalItemProperty>
            <cbc:Name>Item details</cbc:Name>
            <cbc:Value>Item Value</cbc:Value>
         </cac:AdditionalItemProperty>
      </cac:Item>
      <cac:Price>
         <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
         <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:Amount currencyID="AED">0.1</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
         </cac:AllowanceCharge>
      </cac:Price>
   </cac:InvoiceLine>
</Invoice>
