# 🏭 PRODUCTION DEPLOYMENT CHECKLIST

## ✅ **MANDATORY STEPS FOR PRODUCTION**

### **1. 🔐 CERTIFICATES (CRITICAL)**

#### **❌ Current (Testing)**
- Self-signed certificates in `keystore/as4-keystore.p12`
- Not accepted by Peppol network

#### **✅ Required for Production**
- [ ] **Apply for Peppol Certificate** from authorized CA
- [ ] **Get Peppol Access Point Certificate** (from Digicert, GlobalSign, etc.)
- [ ] **Replace dummy certificates** with real ones:
  ```
  keystore/peppol-production-keystore.p12   # Your AP certificate + private key
  keystore/peppol-ca-truststore.p12         # Peppol CA root certificates
  ```
- [ ] **Update certificate passwords** in environment variables

### **2. 📋 PARTICIPANT REGISTRATION (CRITICAL)**

#### **❌ Current (Testing)**
- Fake participant IDs: `9908:*********`, `9908:*********`

#### **✅ Required for Production**
- [ ] **Register with Peppol Authority** in your country
- [ ] **Get official participant ID** (usually based on company registration)
- [ ] **Register in SMP** (Service Metadata Publisher)
- [ ] **Update configuration** with real participant IDs
- [ ] **Test with trading partners** before going live

### **3. 🌐 ENDPOINT DISCOVERY (CRITICAL)**

#### **❌ Current (Testing)**
```java
// Direct endpoint - bypasses Peppol discovery
builder.endpointURL("https://httpbin.org/post");
```

#### **✅ Required for Production**
```java
// Remove direct endpoint - enable SMP discovery
Phase4PeppolSender.builder()
    .documentTypeID(documentType)
    .senderParticipantID(realSenderId)
    .receiverParticipantID(realReceiverId)
    .payload(invoiceBytes)
    // NO .endpointURL() - Phase4 discovers automatically
    .sendMessageAndCheckForReceipt();
```

### **4. ⚙️ CONFIGURATION CHANGES**

#### **Environment Variables (MANDATORY)**
```bash
# Certificate Configuration
export PEPPOL_KEYSTORE_PASSWORD="your-real-keystore-password"
export PEPPOL_KEY_PASSWORD="your-real-private-key-password"
export PEPPOL_TRUSTSTORE_PASSWORD="your-real-truststore-password"

# Participant IDs
export PEPPOL_SENDER_ID="9908:YOUR-REAL-COMPANY-ID"
export PEPPOL_RECEIVER_ID="9908:CUSTOMER-REAL-COMPANY-ID"

# Optional: Custom SMP
export PEPPOL_SMP_URL="https://your-custom-smp.com"
```

#### **Application Properties**
```properties
# Remove or comment out for production
# as4.endpoint.url=  # REMOVE THIS LINE

# Use environment variables
as4.keystore.password=${PEPPOL_KEYSTORE_PASSWORD}
peppol.sender.participant.id=${PEPPOL_SENDER_ID}
```

### **5. 🔒 SECURITY HARDENING**

- [ ] **Enable HTTPS** for your application
- [ ] **Secure keystore files** (proper file permissions)
- [ ] **Use environment variables** for sensitive data
- [ ] **Enable audit logging** for compliance
- [ ] **Set up monitoring** for AS4 transactions
- [ ] **Configure firewall** rules for Peppol network access

### **6. 🧪 TESTING BEFORE PRODUCTION**

#### **Test Environment Steps**
- [ ] **Test with Peppol Test Network** first
- [ ] **Validate certificates** with test participants
- [ ] **Test SMP discovery** with real participant IDs
- [ ] **Verify message delivery** end-to-end
- [ ] **Test error handling** and retry mechanisms

#### **Production Readiness**
- [ ] **Load testing** with expected volume
- [ ] **Failover testing** for high availability
- [ ] **Backup and recovery** procedures
- [ ] **Monitoring and alerting** setup

### **7. 📊 MONITORING & COMPLIANCE**

- [ ] **Transaction logging** for audit trails
- [ ] **Performance monitoring** (response times, throughput)
- [ ] **Error rate monitoring** and alerting
- [ ] **Certificate expiry monitoring**
- [ ] **Compliance reporting** for Peppol Authority

## 🚨 **CRITICAL PRODUCTION DIFFERENCES**

### **Code Changes Required:**

1. **Remove Direct Endpoints**
   ```java
   // REMOVE these lines for production:
   builder.endpointURL(endpointUrl);
   builder.endpointDetailProvider(provider);
   ```

2. **Enable Real Certificate Validation**
   ```java
   // Production mode should NOT bypass certificate validation
   if ("production".equals(as4Mode)) {
       // Use real Peppol certificates
       // Let Phase4 handle SMP discovery
   }
   ```

3. **Real Participant IDs**
   ```java
   // Must be real registered Peppol participant IDs
   IParticipantIdentifier sender = PeppolIdentifierFactory.INSTANCE
       .createParticipantIdentifierWithDefaultScheme("9908:REAL-COMPANY-ID");
   ```

## 🎯 **DEPLOYMENT STEPS**

1. **Obtain Peppol Certificates** (can take weeks)
2. **Register Participant IDs** with Peppol Authority
3. **Set up SMP registration** for your participant
4. **Update application configuration** with real values
5. **Test in Peppol Test Network** thoroughly
6. **Deploy to production** with monitoring
7. **Verify with trading partners** before going live

## ⚠️ **COMMON PRODUCTION PITFALLS**

- **Certificate Issues**: Wrong CA, expired certificates, incorrect key usage
- **Participant ID Issues**: Not registered, wrong format, not in SMP
- **SMP Issues**: Incorrect metadata, wrong endpoints, certificate mismatches
- **Network Issues**: Firewall blocking, DNS resolution problems
- **Compliance Issues**: Missing audit logs, incorrect document formats

## 📞 **SUPPORT CONTACTS**

- **Peppol Authority**: Contact your national Peppol Authority
- **Certificate Authority**: Support from your CA provider
- **Phase4 Library**: Check Phase4 documentation and GitHub issues
- **Trading Partners**: Coordinate testing with your customers/suppliers
